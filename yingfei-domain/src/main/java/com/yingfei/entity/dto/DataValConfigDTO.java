package com.yingfei.entity.dto;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;


/**
 * 检查计划节点选择数据库取值实体类
 */
@Data
@ApiModel
public class DataValConfigDTO extends BaseEntity {

    /**
     * 查询条件
     * key:
     * @see com.yingfei.entity.enums.DataValTypeEnum
     * value:  json字符串  格式{"id":"","name":""}
     */
    @ApiModelProperty("查询条件 value:  json字符串  格式{\"id\":\"\",\"name\":\"\"}")
    Map<String, JSONObject> map;

    /**
     * 时间类型(0:秒 1:分 2:时 3:天)
     */
    @ApiModelProperty("时间类型(0:秒 1:分 2:时 3:天)")
    Integer timeType;

    /**
     * 时间范围
     */
    @ApiModelProperty("时间范围")
    Integer timeNum;

    /**
     * 符合标准类型(0:最后一个值保存至数据库  1:数据库多个值特殊处理)
     */
    @ApiModelProperty("符合标准类型(0:最后一个值保存至数据库  1:数据库多个值特殊处理)")
    Integer standardType;

    /**
     * 特殊处理类型(0:标准差 1:极差 2:均值 3:频数 4:求和 5:中位数 6:最大值 7:最小值)
     */
    @ApiModelProperty("特殊处理类型(0:标准差 1:极差 2:均值 3:频数 4:求和 5:中位数 6:最大值 7:最小值)")
    Integer specialHandlingType;

    /**
     * 将特殊处理限制在最后输入的子组。
     */
    @ApiModelProperty("将特殊处理限制在最后输入的子组")
    Boolean isDisposeEndSgrp = false;

    /**
     * 如果未发现数据库取值，返回.
     */
    @ApiModelProperty("如果未发现数据库取值，返回")
    Double nullReturn;
}
