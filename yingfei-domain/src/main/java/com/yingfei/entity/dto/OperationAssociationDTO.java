package com.yingfei.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 操作日志数据影响关联类
 */
@Data
@Accessors(chain = true)
public class OperationAssociationDTO {

    /**
     * 操作模块
     */
    String title;

    /**
     * @see com.yingfei.common.log.enums.BusinessType
     * 操作类型
     */
    String operationType;

    /**
     * 影响表名
     */
    String tableName;

    /**
     * 删除id列表
     */
    List<Long> influenceIdList;

    /**
     * 删除影响id列表
     */
    Map<Long, List<Long>> influenceIdMap;

    /**
     * 修改前内容
     */
    Object originalRecord;

    /**
     * 关联内容
     */
    List<OperationAssociationDTO> cascadeRecordList;
}
