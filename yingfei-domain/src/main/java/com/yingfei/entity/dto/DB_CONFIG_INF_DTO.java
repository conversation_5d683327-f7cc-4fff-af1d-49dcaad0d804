package com.yingfei.entity.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 数据库配置
* @TableName DB_CONFIG_INF
*/
@Data
public class DB_CONFIG_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DBCO;
    /**
    * 数据库名称
    */
    @ApiModelProperty("数据库名称")
    private String F_NAME;
    /**
    * 数据库主链接
    */
    @ApiModelProperty("数据库主链接")
    private String F_PREFIX;
    /**
    * 数据库驱动
    */
    @ApiModelProperty("数据库驱动")
    private String F_DRIVER;
    /**
    * 数据库链接表名配置
    */
    @ApiModelProperty("数据库链接表名配置")
    private String F_SUFFIX;

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 数据库账号
     */
    private String F_USERNAME;

    /**
     * 数据库密码
     */
    private String F_PASSWORD;
}
