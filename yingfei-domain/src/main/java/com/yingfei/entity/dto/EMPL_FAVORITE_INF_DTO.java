package com.yingfei.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import lombok.Data;

/**
 * 用户菜单收藏表
 */
@Data
public class EMPL_FAVORITE_INF_DTO extends BaseEntity {
    /**
     * 记录主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ID;

    /**
     * 保存用户id
     */
    private String F_EMPL_ID;

    /**
     * 菜单ID
     */
    private String F_MENU_ID;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单类型
     */
    private Integer pageType;
}