package com.yingfei.entity.dto;

import com.yingfei.entity.enums.ControlChartSingleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 计算出来的单张控制限
 */
@Data
public class CalculatedControlLimit {
    /**
     * 图表类型
     */
    private ControlChartSingleEnum chartSingleEnum;

    /**
     * 计算出的CL值
     */
    @ApiModelProperty("计算出的CL值")
    private Double CL;

    /**
     * 计算出的UCL值
     */
    @ApiModelProperty("计算出的UCL值")
    private Double UCL;

    /**
     * 计算出的LCL值
     */
    @ApiModelProperty("计算出的LCL值")
    private Double LCL;

    /**
     * 计算出的数据点值
     */
    @ApiModelProperty("计算出的数据点值")
    private Double dataPoint;

    /**
     * 上A
     */
    private Double areaUpA;
    /**
     * 上B
     */
    private Double areaUpB;
    /**
     * 下A
     */
    private Double areaDownA;
    /**
     * 下B
     */
    private Double areaDownB;

    /**
     * 计算A区,B区
     *
     * @param calculatedControlLimit
     */
    public static void calculateSection(CalculatedControlLimit calculatedControlLimit) {
        /*计算上区间间距*/
        double a = (calculatedControlLimit.getUCL() - calculatedControlLimit.getCL()) / 3;
        /*计算下区间间距*/
        double b = (calculatedControlLimit.getCL() - calculatedControlLimit.getLCL()) / 3;
        calculatedControlLimit.setAreaUpA(calculatedControlLimit.getUCL() - a);
        calculatedControlLimit.setAreaDownA(calculatedControlLimit.getLCL() + b);

        calculatedControlLimit.setAreaUpB(calculatedControlLimit.getCL() + a);
        calculatedControlLimit.setAreaDownB(calculatedControlLimit.getCL() - b);
    }
}
