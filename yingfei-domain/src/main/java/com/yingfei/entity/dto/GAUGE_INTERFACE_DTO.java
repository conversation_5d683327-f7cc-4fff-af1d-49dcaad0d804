package com.yingfei.entity.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 量具接口配置表
* @TableName GAUGE_INTERFACE
*/
@Data
public class GAUGE_INTERFACE_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAIN;
    /**
    * 串值初始化json
    */
    @ApiModelProperty("串值初始化json")
    private String F_INIT_DATA;
    /**
    * 延迟多少毫秒
    */
    @ApiModelProperty("延迟多少毫秒")
    private Integer F_DELAY;
    /**
    * 初始值串
    */
    @ApiModelProperty("初始值串")
    private String F_INIT_STR;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
    * 量具接口名称
    */
    @ApiModelProperty("量具接口名称")
    private String F_NAME;
    
}
