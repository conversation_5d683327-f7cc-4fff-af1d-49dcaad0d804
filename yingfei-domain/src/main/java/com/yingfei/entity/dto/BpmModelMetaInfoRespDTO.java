package com.yingfei.entity.dto;

import lombok.Data;

/**
 * BPM 流程 MetaInfo Response DTO
 * 主要用于 { Model#setMetaInfo(String)} 的存储
 *
 * <AUTHOR>
 */
@Data
public class BpmModelMetaInfoRespDTO {

    /**
     * 流程描述
     */
    private String description;
    /**
     * 表单类型
     */
    private Integer formType;
    /**
     * 表单编号
     * @see com.yingfei.entity.enums.BpmModelFormTypeEnum 在表单类型为 {#NORMAL} 时
     */
    private Long formId;
    /**
     * 自定义表单的提交路径，使用 Vue 的路由地址
     * @see com.yingfei.entity.enums.BpmModelFormTypeEnum 在表单类型为 {#CUSTOM} 时
     */
    private String formCustomCreatePath;
    /**
     * 自定义表单的查看路径，使用 Vue 的路由地址
     * @see com.yingfei.entity.enums.BpmModelFormTypeEnum 在表单类型为 {#CUSTOM} 时
     */
    private String formCustomViewPath;

}
