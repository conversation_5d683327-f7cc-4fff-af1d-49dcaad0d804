package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 量具解析参数配置类
 */
@Data
@ApiModel
public class GAUGE_FORMAT_CONFIG_DTO {

    /**
     * 字段名
     */
    @ApiModelProperty("字段名")
    String fieldName;

    /**
     * 字段号
     */
    @ApiModelProperty("字段号")
    Integer fieldNum;

    /**
     * 启动位置
     */
    @ApiModelProperty("启动位置")
    Integer start;

    /**
     * 长度
     */
    @ApiModelProperty("长度")
    Integer length;

    /**
     * 类型(0:频道号  1:测试值)
     */
    Integer type = 1;

    /**
     * 解析出来的值
     */
    @ApiModelProperty("解析出来的值")
    String value;
}
