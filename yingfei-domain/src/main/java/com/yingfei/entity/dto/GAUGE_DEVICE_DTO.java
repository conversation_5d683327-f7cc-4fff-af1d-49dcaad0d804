package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 量具设备配置表
* @TableName GAUGE_DEVICE
*/
@Data
public class GAUGE_DEVICE_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GADE;

    @ApiModelProperty("描述")
    private String F_NAME;

    /**
    * 量具连接参数表id
    */
    @ApiModelProperty("量具连接参数表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GICP;
    /**
    * 量具解析规则配置表id
    */
    @ApiModelProperty("量具解析规则配置表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAFO;
    /**
    * 配置json
    */
    @ApiModelProperty("配置json")
    private String F_CONFIG;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 量具设备配置
     */
    @ApiModelProperty("量具设备配置")
    private GAUGE_DEVICE_CONFIG_DTO gaugeDeviceConfigDto;

    @ApiModelProperty("量具解析规则名称")
    private String gaFoName;

    @ApiModelProperty("量具连接信息")
    private GAUGE_CONNECTION_DTO gaugeConnectionDto;

    @ApiModelProperty("量具解析规则")
    private GAUGE_FORMAT_DTO gaugeFormatDto;
}
