package com.yingfei.entity.dto;

import lombok.Data;

/**
 * 账户安全配置类
 */
@Data
public class EmplSecurityConfigDTO {

    /**
     * 密码长度
     */
    private Integer pwdLength = 5;

    /**
     * 包含大小写
     */
    private Boolean isIncludeCase = false;

    /**
     * 包含数字和字母
     */
    private Boolean isNumbersAndLetters = false;

    /**
     * 包含特殊字符
     */
    private Boolean isIncludeSpecial = false;

    /**
     * 密码历史记录长度。
     */
    private Integer pwdHistoryLength;

    /**
     * 密码历史记录单位:月和年
     * @see com.yingfei.entity.enums.TimeEnum
     */
    private Integer pwdHistoryUnit;

    /**
     * 用户名长度
     */
    private Integer accountNameLength = 5;

    /**
     * 登录失败X次后账户锁定1小时
     */
    private Integer loginFailures;

    /**
     * 账户闲置超过X小时后退出登录
     */
    private Integer accountIdleTime;

    /**
     * 账户X天未登录后自动失效
     */
    private Integer loginFailureTime;
}
