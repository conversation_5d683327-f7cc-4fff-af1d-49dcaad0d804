package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实时质量概览返回类
 */
@Data
@ApiModel
public class QualityOverviewDTO {

    @ApiModelProperty("工艺流程名称")
    private String flowName;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("抽样数量")
    private Integer sampleNum;

    @ApiModelProperty("报警率")
    private Double alarmRate;

    @ApiModelProperty("解决率")
    private Double resolutionRate;
}
