package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户菜单列配置json对象
 */
@Data
@ApiModel
public class LAYOUT_INF_DATA_DTO {

    /**
     * 列名
     */
    @ApiModelProperty("列名")
    private String title;

    /**
     * 列顺序
     */
    @ApiModelProperty("列顺序")
    private Integer columnSort;

    /**
     * 是否冻结
     */
    @ApiModelProperty("是否冻结")
    private Boolean frozen = false;

    /**
     * 是否搜索
     */
    @ApiModelProperty("是否搜索")
    private Boolean searchable = false;

    /**
     * 是否隐藏显示
     */
    @ApiModelProperty("是否隐藏显示")
    private Boolean hidden = false;

    /**
     * 位置
     */
    private String align;

    private String dataIndex;

    private String key;

    /**
     * 宽度
     */
    private Double width;

    /**
     * 默认分页
     */
    private Integer page;
}
