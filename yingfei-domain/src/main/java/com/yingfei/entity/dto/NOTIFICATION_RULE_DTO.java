package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class NOTIFICATION_RULE_DTO {

    /**
     * 记录主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_NOTI;

    /**
     * 报警通知规则名称
     */
    private String F_NAME;

    /**
     * 报警筛选范围json
     *
     * @see com.yingfei.entity.dto.NotificationDataJsonDTO
     */
    private String F_DATA;

    /**
     * 报警类型(1:公差限 2:控制限)
     */
    private Integer F_ALR_TYPE;

    /**
     * 具体报警(见对应的公差限枚举和控制限枚举
     *
     * @see com.yingfei.entity.enums.SpecificationLimitViolation
     * @see com.yingfei.entity.enums.StatisticalViolationTypeEnum
     * )
     */
    private String F_ALR_DETAIL;

    /**
     * 收件人(多个逗号分割)
     */
    private String F_EMPL;

    /**
     * 通知类型,可多选(1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知 5:其他通知)
     */
    private String F_NOTI_TYPE;

    /**
     * 处理方式(
     * 0:不做处理
     * 1:填写异常原因
     * 2:填写改善措施
     * 3:填写异常原因和改善措施
     * )
     */
    private Integer F_STATUS;

    /**
     * 流程定义的编号(为空不走流程)
     */
    private String F_PROCESS_DEFINITION;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 异常原因所选条件
     *
     * @see com.yingfei.entity.dto.NotificationCauseDataDTO
     */
    private String F_CAUSE_DATA;

    /**
     * 改善措施所选条件
     *
     * @see com.yingfei.entity.dto.NotificationCauseDataDTO
     */
    private String F_ACTION_DATA;

    /**
     * 0:用户 1:角色
     */
    private Integer F_EMPL_TYPE;

    /**
     * 描述
     */
    private String F_DESC;

    /**
     * 工厂id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT;

    /**
     * 异常原因所选条件对象
     */
    private NotificationCauseDataDTO causeDataDTO;

    /**
     * 改善措施所选条件对象
     */
    private NotificationCauseDataDTO actionDataDTO;
}
