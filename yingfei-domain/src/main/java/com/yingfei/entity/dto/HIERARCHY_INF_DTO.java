package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.enums.HIERARCHY_INFTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 层级结构表
 *
 * @TableName HIERARCHY_INF
 */
@Data
public class HIERARCHY_INF_DTO extends BaseEntity {

    /**
     * 记录主键
     */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_HIER;
    /**
     * 部门名称
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("部门名称")
    @Length(max = 50, message = "编码长度不能超过50")
    private String F_NAME;
    /**
     * 父级主键
     */
    @ApiModelProperty("父级主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PARENT;

    /**
     * 层级类型(0:集团 1:事业部  2:工厂  3:部门  4:车间)
     */
    @ApiModelProperty("层级类型(0:集团 1:事业部  2:工厂  3:部门  4:车间)")
    private Integer F_TYPE;
    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 子部门
     */
    @ApiModelProperty("子部门")
    private List<HIERARCHY_INF_DTO> children = new ArrayList<>();

    /**
     * 对应产品列表
     */
    private List<PART_INF_DTO> partInfList;

    /**
     * 对应过程列表
     */
    private List<PRCS_INF_DTO> prcsInfList;

    /**
     * 对应测试列表
     */
    private List<TEST_INF_DTO> testInfList;

    /**
     * 获取对应类型层级
     */
    public static void getHierarchyInfList(List<HIERARCHY_INF_DTO> list, HIERARCHY_INF_DTO hierarchyInfDto, Integer type) {
        if (CollectionUtils.isEmpty(hierarchyInfDto.getChildren())) {
            list.add(hierarchyInfDto);
            return;
        }
        if (Objects.equals(hierarchyInfDto.getF_TYPE(), type)) list.add(hierarchyInfDto);
        for (HIERARCHY_INF_DTO child : hierarchyInfDto.getChildren()) {
            if (Objects.equals(child.getF_TYPE(), type)) {
                list.add(child);
                continue;
            }
            if (CollectionUtils.isNotEmpty(child.getChildren())) {
                getHierarchyInfList(list, child, type);
            }
        }
    }

    public static List<Long> getHierarchyInfIds(HIERARCHY_INF_DTO hierarchyInfDto) {
        List<HIERARCHY_INF_DTO> hierarchyInfDtoList = new ArrayList<>();
        HIERARCHY_INF_DTO.getHierarchyInfList(hierarchyInfDtoList, hierarchyInfDto, HIERARCHY_INFTypeEnum.FACTORY.getType());
        return hierarchyInfDtoList.stream().map(HIERARCHY_INF_DTO::getF_HIER).collect(Collectors.toList());
    }
}
