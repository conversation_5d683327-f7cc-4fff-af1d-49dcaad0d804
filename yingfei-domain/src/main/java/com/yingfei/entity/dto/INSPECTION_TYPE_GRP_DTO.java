package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import lombok.Data;

/**
 * 储存检验类型组信息表
 * @TableName INSPECTION_TYPE_GRP
 */
@Data
public class INSPECTION_TYPE_GRP_DTO extends BaseEntity {
    /**
     * 记录主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ID;

    /**
     * 检验类型组名称
     */
    private String F_NAME;

    /**
     * 因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;


}