package com.yingfei.entity.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 量具连接参数表
* @TableName GAUGE_CONNECTION
*/
@Data
public class GAUGE_CONNECTION_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GICP;
    /**
    * 量具接口表id
    */
    @ApiModelProperty("量具接口表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAIN;
    /**
    * Agent表id
    */
    @ApiModelProperty("Agent表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAAG;
    /**
    * 端口设置json(com口和通讯参数)
    */
    @ApiModelProperty("端口设置json(com口和通讯参数)")
    private String F_CONFIG;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @ApiModelProperty("量具接口配置信息")
    private GAUGE_INTERFACE_DTO gaugeInterfaceDto;

    @ApiModelProperty("量具连接配置")
    private GAUGE_CONNECTION_CONFIG_DTO gaugeConnectionConfigDto;

    @ApiModelProperty("量具Agent名称")
    private String agentName;

    @ApiModelProperty("量具Agent信息")
    private GAUGE_AGENT_DTO gaugeAgentDto;
}
