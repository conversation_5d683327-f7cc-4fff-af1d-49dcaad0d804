package com.yingfei.entity.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 通知规则报警类型DTO
 */
@Data
public class NotificationAlrDTO {

    /**
     * 报警类型(1:公差限 2:控制限)
     */
    private Integer alrType;

    /**
     * 具体报警(见对应的公差限枚举和控制限枚举)
     *  公差限见枚举  控制限为报警规则信息表id
     * @see com.yingfei.entity.enums.SpecificationLimitViolation
     * @see com.yingfei.entity.enums.StatisticalViolationTypeEnum
     *
     */
    private List<String> alrIds = new ArrayList<>();
}
