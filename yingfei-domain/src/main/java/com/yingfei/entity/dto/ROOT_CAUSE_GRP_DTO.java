package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;

/**
* 储存异常原因组信息表
* @TableName ROOT_CAUSE_GRP
*/
@Data
public class ROOT_CAUSE_GRP_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RCGP;
    /**
    * 分公司主键
    */
    @ApiModelProperty("分公司主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DIV = 0L;
    /**
    * 异常原因组名称
    */
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("异常原因组名称")
    @Length(max= 100,message="编码长度不能超过100")
    private String F_NAME;
    /**
    * 异常原因组因子，默认为1
    */
    @ApiModelProperty("异常原因组因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

}
