package com.yingfei.entity.dto;

import com.yingfei.entity.enums.TimeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TaskScheduleModel {
    TimeEnum timeEnum;

    /**一周的哪几天*/
    Integer[] dayOfWeeks;

    /**一个月的哪几天*/
    Integer[] dayOfMonths;

    /**秒  */
    Integer second = 0;

    /**分  */
    Integer minute = 0;

    /**时  */
    Integer hour = 0;

    /**
     * 年
     */
    Integer[] months;

    /**
     * 间隔
     */
    Integer beApart;

}
