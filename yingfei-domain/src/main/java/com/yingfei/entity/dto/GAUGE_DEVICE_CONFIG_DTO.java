package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 量具设备配置表
 *
 * @TableName GAUGE_DEVICE
 */
@Data
@ApiModel
public class GAUGE_DEVICE_CONFIG_DTO {

    /**
     * 频道号
     */
    @ApiModelProperty("频道号")
    String channelNum;

    /**
     * 量具标识符
     */
    @ApiModelProperty("量具标识符")
    String tag;

    /**
     * 量具序列号
     */
    @ApiModelProperty("量具序列号")
    String serial;

    @ApiModelProperty("量具获取值(测量 X 设置值)")
    Double fetchValue = 1d;

    @ApiModelProperty("量具归零值(测量 - 归零值)")
    Double zeroValue = 0d;

    @ApiModelProperty("是否返回测量绝对值")
    Boolean absoluteValue = false;

}
