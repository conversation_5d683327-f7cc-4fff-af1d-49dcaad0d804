package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报警通知数据范围json
 */
@Data
public class NotificationDataJsonDTO {

    /**
     * 是否包含(0:否  1:是)
     */
    @ApiModelProperty("是否包含(0:否  1:是)")
    private Integer isInclude = 1;

    /**
     * 是否动态(0:否  1:是)
     */
    @ApiModelProperty("是否动态(0:否  1:是)")
    private Integer isDynamic = 0;

    /**
     * 产品,过程,测试对应数据列表(静态时)
     */
    @ApiModelProperty("产品,过程,测试对应数据列表(静态时)")
    private List<String> dataList = new ArrayList<>();

    /**
     * 动态存入的标签组
     * key:组id  value: 对应的项id列表
     */
    @ApiModelProperty("* 动态存入的标签组" +
            "  * key:组id  value: 对应的项id列表")
    private Map<String, List<String>> dataMap = new HashMap<>();

    /**
     * 类型(0:产品  1:过程  2:测试 )
     */
    @ApiModelProperty("类型(0:产品  1:过程  2:测试)")
    private Integer type;


}
