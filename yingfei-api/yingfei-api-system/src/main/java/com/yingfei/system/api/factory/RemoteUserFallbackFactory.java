package com.yingfei.system.api.factory;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.entity.dto.AddFriendUserDto;
import com.yingfei.entity.dto.AdminUserRespDTO;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.model.LoginUser;
import com.yingfei.entity.vo.EMPL_INF_VO;
import com.yingfei.system.api.RemoteUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户服务降级处理
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable) {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService() {
            @Override
            public R<LoginUser> getUserInfo(String username, String source) {
                return R.fail(I18nUtils.getMessage("GET_USER_FAILED") + ":" + throwable.getMessage());
            }

            @Override
            public R<List<AddFriendUserDto>> getUserLikeUserName(String userName, String source) {
                return R.fail(I18nUtils.getMessage("GET_USER_FAILED") + ":" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> getLoginUserInfo(String phonenumber, String source) {
                return R.fail(I18nUtils.getMessage("GET_USER_FAILED") + ":" + throwable.getMessage());
            }

            @Override
            public R<Map<Long, AdminUserRespDTO>> getUserMap(EMPL_INF_VO emplInfVo) {
                return R.fail(I18nUtils.getMessage("GET_USER_FAILED") + ":" + throwable.getMessage());
            }

            @Override
            public R<AdminUserRespDTO> getUser(Long userId) {
                return R.fail(I18nUtils.getMessage("GET_USER_FAILED") + ":" + throwable.getMessage());
            }

            @Override
            public R<Set<Long>> findByIds(String condition, Integer type) {
                return R.fail(I18nUtils.getMessage("GET_USER_FAILED") + ":" + throwable.getMessage());
            }

            @Override
            public R<EMPL_INF_DTO> info(Long id) {
                return R.fail(I18nUtils.getMessage("GET_USER_FAILED") + ":" + throwable.getMessage());
            }

            @Override
            public R<HIERARCHY_INF_DTO> findByHierId(Long id) {
                return R.fail(I18nUtils.getMessage("GET_HIERARCHY_FAILED") + ":" + throwable.getMessage());
            }

            @Override
            public R<List<EMPL_INF_DTO>> getList(EMPL_INF_VO emplInfVo) {
                return R.fail();
            }
        };
    }
}
