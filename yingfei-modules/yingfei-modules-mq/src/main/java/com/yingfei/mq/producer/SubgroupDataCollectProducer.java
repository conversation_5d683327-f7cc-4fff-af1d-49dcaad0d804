package com.yingfei.mq.producer;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.entity.domain.MESSAGE_LOG_INF;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.dataImport.FileNameDataDTO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.mq.config.RabbitConfig;
import com.yingfei.mq.service.MESSAGE_LOG_INFService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 子组采集生产者
 */
@Component
@Slf4j
public class SubgroupDataCollectProducer {

    @Resource
    RabbitTemplate rabbitTemplate;
    @Resource
    private MESSAGE_LOG_INFService messageLogInfService;
    @Resource
    private RedisService redisService;


    /**
     * 发送消息到队列
     *
     * @param subgroupDataVOList
     */
    public void send(List<SubgroupDataVO> subgroupDataVOList) {
        Long aLong = JudgeUtils.defaultIdentifierGenerator.nextId(null);
        CorrelationData correlationData = new CorrelationData(String.valueOf(aLong));
        String message = JSONObject.toJSONString(subgroupDataVOList);

        EMPL_INF_DTO emplInfDto = redisService.getCacheObject(RedisConstant.ADMIN_USER_ID);

        /*保存消息日志*/
        MESSAGE_LOG_INF messageLogInf = new MESSAGE_LOG_INF();
        messageLogInf.setF_NEWS(Long.valueOf(correlationData.getId()));
        messageLogInf.setF_DATA(subgroupDataVOList.size() < 5 ? message : I18nUtils.getMessage("DATA_VOLUME_TOO_LARGE"));
        messageLogInf.setF_CRUE(emplInfDto.getF_EMPL());
        messageLogInfService.save(messageLogInf);
        redisService.setCacheObject(RedisConstant.MESSAGE_LOG + correlationData.getId(), messageLogInf, 10L);

        RabbitConfig.executor.schedule(() -> {
            rabbitTemplate.convertAndSend(RabbitConfig.NORMAL_EXCHANGE_NAME,
                    RabbitConfig.NORMAL_ROUTING_KEY,
                    message,
                    correlationData);
        }, 1, TimeUnit.SECONDS);
    }

    public void batchSend(FileNameDataDTO fileNameDataDTO) {
        Long aLong = JudgeUtils.defaultIdentifierGenerator.nextId(null);
        CorrelationData correlationData = new CorrelationData(String.valueOf(aLong));

        EMPL_INF_DTO emplInfDto = redisService.getCacheObject(RedisConstant.ADMIN_USER_ID);

        String jsonString = JSONObject.toJSONString(fileNameDataDTO);

        /*保存消息日志*/
        MESSAGE_LOG_INF messageLogInf = new MESSAGE_LOG_INF();
        messageLogInf.setF_NEWS(Long.valueOf(correlationData.getId()));
        messageLogInf.setF_DATA(jsonString);
        messageLogInf.setF_CRUE(emplInfDto.getF_EMPL());
        messageLogInf.setF_TYPE(3);
        messageLogInfService.save(messageLogInf);
        redisService.setCacheObject(RedisConstant.MESSAGE_LOG + correlationData.getId(), messageLogInf, 10L);

        RabbitConfig.executor.schedule(() -> {
            rabbitTemplate.convertAndSend(RabbitConfig.NORMAL_EXCHANGE_NAME,
                    RabbitConfig.NORMAL_ROUTING_KEY,
                    jsonString,
                    correlationData);
        }, 1, TimeUnit.SECONDS);
    }
}
