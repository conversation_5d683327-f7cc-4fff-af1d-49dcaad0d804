package com.yingfei.mq.producer;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.mq.config.RabbitConfig;
import com.yingfei.mq.consumer.SubgroupCacheDTO;
import com.yingfei.mq.service.MESSAGE_LOG_INFService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.UnknownHostException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 子组缓存生产者
 */
@Component
@Slf4j
public class SubgroupDataCacheProducer {

    @Resource
    RabbitTemplate rabbitTemplate;
    @Resource
    private MESSAGE_LOG_INFService messageLogInfService;
    @Resource
    private RedisService redisService;

    /**
     * 发送消息到队列
     *
     * @param subgroupDataVOList
     */
    public void send(List<SubgroupDataVO> subgroupDataVOList, List<SGRP_INF> sgrpInfList, List<SGRP_VAL> sgrpValList, List<SGRP_DSC> sgrpDscList, List<SGRP_CMT> sgrpCmtList) throws UnknownHostException {
        Long aLong = JudgeUtils.defaultIdentifierGenerator.nextId(null);
        CorrelationData correlationData = new CorrelationData(String.valueOf(aLong));
        SubgroupCacheDTO subgroupCacheDTO = new SubgroupCacheDTO();
        subgroupCacheDTO.setSubgroupDataVOList(subgroupDataVOList);
        subgroupCacheDTO.setSgrpInfList(sgrpInfList);
        subgroupCacheDTO.setSgrpValList(sgrpValList);
        subgroupCacheDTO.setSgrpDscList(sgrpDscList);
        subgroupCacheDTO.setSgrpCmtLis(sgrpCmtList);

        EMPL_INF_DTO emplInfDto = redisService.getCacheObject(RedisConstant.ADMIN_USER_ID);
        /*保存消息日志*/
        String message = JSONObject.toJSONString(subgroupCacheDTO);
        MESSAGE_LOG_INF messageLogInf = new MESSAGE_LOG_INF();
        messageLogInf.setF_NEWS(Long.valueOf(correlationData.getId()));
        messageLogInf.setF_DATA(subgroupDataVOList.size() < 5 ? JSONObject.toJSONString(subgroupDataVOList) : I18nUtils.getMessage("DATA_VOLUME_TOO_LARGE"));
        messageLogInf.setF_CRUE(emplInfDto.getF_EMPL());
        messageLogInf.setF_TYPE(1);
        messageLogInfService.save(messageLogInf);

        redisService.setCacheObject(RedisConstant.MESSAGE_LOG + correlationData.getId(), messageLogInf, 10L);
        /*延时一秒后执行 不延时的话消费者查不到消息日志*/
        RabbitConfig.executor.schedule(() -> {
            rabbitTemplate.convertAndSend(RabbitConfig.CACHE_EXCHANGE_NAME,
                    RabbitConfig.CACHE_ROUTING_KEY,
                    message,
                    correlationData);
        }, 1, TimeUnit.SECONDS);

    }
}
