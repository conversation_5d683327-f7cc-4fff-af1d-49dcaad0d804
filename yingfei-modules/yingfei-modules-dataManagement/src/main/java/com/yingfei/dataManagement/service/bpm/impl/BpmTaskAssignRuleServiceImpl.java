package com.yingfei.dataManagement.service.bpm.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.annotations.VisibleForTesting;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.constant.WorkFlowConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.CollectionToMapUtils;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.dataManagement.mapper.convert.BpmTaskAssignRuleConvert;
import com.yingfei.dataManagement.service.bpm.BpmFormService;
import com.yingfei.dataManagement.service.bpm.script.BpmTaskAssignScript;
import com.yingfei.dataManagement.mapper.BpmTaskAssignRuleMapper;
import com.yingfei.dataManagement.service.bpm.BpmModelService;
import com.yingfei.dataManagement.service.bpm.BpmProcessDefinitionService;
import com.yingfei.dataManagement.service.bpm.BpmTaskAssignRuleService;
import com.yingfei.entity.domain.BPM_TASK_RULE;
import com.yingfei.entity.dto.BPM_FROM_DTO;
import com.yingfei.entity.dto.BPM_TASK_RULE_DTO;
import com.yingfei.entity.enums.BpmTaskAssignRuleTypeEnum;
import com.yingfei.entity.vo.BPM_TASK_RULE_VO;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.xml.instance.DomElement;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.format;
import static com.yingfei.common.core.utils.CollectionToMapUtils.convertMap;

/**
 * BPM 任务分配规则 Service 实现类
 */
@Service
@Validated
@Slf4j
public class BpmTaskAssignRuleServiceImpl extends ServiceImpl<BpmTaskAssignRuleMapper, BPM_TASK_RULE> implements BpmTaskAssignRuleService {

    @Resource
    private BpmTaskAssignRuleMapper taskRuleMapper;
    @Resource
    @Lazy // 解决循环依赖
    private BpmModelService modelService;
    @Resource
    @Lazy // 解决循环依赖
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private BpmFormService bpmFormService;
    /**
     * 任务分配脚本
     */
    private Map<Long, BpmTaskAssignScript> scriptMap = Collections.emptyMap();

    /**
     * processDefinitionId 空串，用于标识属于流程模型，而不属于流程定义
     */
    public static final String PROCESS_DEFINITION_ID_NULL = "";

    @Resource
    public void setScripts(List<BpmTaskAssignScript> scripts) {
        this.scriptMap = convertMap(scripts, script -> script.getEnum().getId());
    }

    @Override
    public List<BPM_TASK_RULE_DTO> getTaskAssignRuleListByProcessDefinitionId(String processDefinitionId,
                                                                              String taskDefinitionKey) {
        return selectListByProcessDefinitionId(processDefinitionId, taskDefinitionKey);
    }

    public List<BPM_TASK_RULE_DTO> selectListByProcessDefinitionId(String processDefinitionId, String taskDefinitionKey) {
        LambdaQueryWrapper<BPM_TASK_RULE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_TASK_RULE::getF_PROCESS_DEFINITION, processDefinitionId)
                .eq(BPM_TASK_RULE::getF_TASK_KEY, taskDefinitionKey);
        List<BPM_TASK_RULE> bpmTaskRules = taskRuleMapper.selectList(queryWrapper);
        return BpmTaskAssignRuleConvert.INSTANCE.convertList(bpmTaskRules);
    }

    @Override
    public List<BPM_TASK_RULE_DTO> getTaskAssignRuleListByModelId(Long modelId) {
        LambdaQueryWrapper<BPM_TASK_RULE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_TASK_RULE::getF_MODE, modelId)
                .eq(BPM_TASK_RULE::getF_PROCESS_DEFINITION, PROCESS_DEFINITION_ID_NULL);
        return BpmTaskAssignRuleConvert.INSTANCE.convertList(taskRuleMapper.selectList(queryWrapper));
    }

    @Override
    public List<BPM_TASK_RULE_DTO> getTaskAssignRuleList(Long modelId, String processDefinitionId) {
        // 获得规则
        List<BPM_TASK_RULE_DTO> rules = Collections.emptyList();
        BpmnModelInstance model = null;
        if (modelId != null) {
            rules = getTaskAssignRuleListByModelId(modelId);
            model = modelService.getBpmnModel(modelId);
        } else if (StrUtil.isNotEmpty(processDefinitionId)) {
            rules = getTaskAssignRuleListByProcessDefinitionId(processDefinitionId, null);
            model = processDefinitionService.getBpmnModel(processDefinitionId);
        }
        if (model == null) {
            return Collections.emptyList();
        }

        List<DomElement> domElementList = model.getDocument().getRootElement().getChildElements();
        if (CollectionUtils.isEmpty(domElementList)) {
            return new ArrayList<>();
        }
        DomElement domElement = domElementList.stream().filter(it -> "process".equals(it.getLocalName())).findFirst().orElse(null);
        HashMap<String, String> seqMap = getSequenceFlow(domElement);

        Collection<StartEvent> startEvents = model.getModelElementsByType(StartEvent.class);
        List<StartEvent> startEventList = new ArrayList<>(startEvents);
        String startId = startEventList.get(0).getId();
        /*生成节点顺序*/
        Map<String, Integer> map = new HashMap<>();
        getNodeOrder(startId, map, seqMap, 1);

        // 获得用户任务，只有用户任务才可以设置分配规则
        Collection<UserTask> userTasks = model.getModelElementsByType(UserTask.class);
        List<UserTask> userTaskList = new ArrayList<>(userTasks);
        if (CollUtil.isEmpty(userTasks)) {
            return Collections.emptyList();
        }
        Map<String, BPM_TASK_RULE_DTO> ruleMap = CollectionToMapUtils.convertMap(rules, BPM_TASK_RULE_DTO::getF_TASK_KEY);
        // 转换数据
        List<BPM_TASK_RULE_DTO> bpmTaskRuleDtos = BpmTaskAssignRuleConvert.INSTANCE.convertList(userTaskList, ruleMap);

        bpmTaskRuleDtos.forEach(bpmTaskRuleDto -> {
            Integer i = map.get(bpmTaskRuleDto.getF_TASK_KEY());
            if (i == null) return;
            bpmTaskRuleDto.setNum(i);
        });

        return bpmTaskRuleDtos.stream().filter(s -> s.getNum() != null).sorted(Comparator.comparing(BPM_TASK_RULE_DTO::getNum)).collect(Collectors.toList());
    }

    private void getNodeOrder(String startId, Map<String, Integer> map, HashMap<String, String> seqMap, Integer num) {
        if (MapUtils.isEmpty(seqMap)) return;
        String s = seqMap.get(startId);
        if (StringUtils.isEmpty(s)) return;
        map.put(s, num);
        seqMap.remove(startId);
        getNodeOrder(s, map, seqMap, ++num);
    }


    /**
     * 获取流程设计的所有连接线
     *
     * @param e
     * @return
     */
    public static HashMap<String, String> getSequenceFlow(DomElement e) {
        HashMap<String, String> sequenceFlow = new HashMap<>();
        e.getChildElements().stream()
                .filter(it -> "sequenceFlow".equals(it.getLocalName()))
                .forEach(item -> {
                    if (sequenceFlow.get(item.getAttribute("sourceRef")) == null) {
                        String sourceRef = item.getAttribute("sourceRef");
                        if (sourceRef.startsWith("StartEvent")) {
                            sequenceFlow.put("StartEvent", item.getAttribute("sourceRef"));
                        }
                        sequenceFlow.put(sourceRef, item.getAttribute("targetRef"));
                    }
//                    else {
//                        sequenceFlow.put(item.getAttribute("sourceRef"), sequenceFlow.get(item.getAttribute("sourceRef")) + "," + item.getAttribute("targetRef"));
//                    }
                });
        return sequenceFlow;
    }

    /**
     * 获取流程设计的所有节点
     *
     * @param e
     * @return
     */
    public static HashMap<String, String> getNode(DomElement e) {
        HashMap<String, String> nodeMap = new HashMap<>();
        e.getChildElements().stream()
                .filter(it -> "startEvent".equals(it.getLocalName()) || "userTask".equals(it.getLocalName()) || "subProcess".equals(it.getLocalName()))
                .forEach(item -> {
                    switch (item.getLocalName()) {
                        case "userTask":
                            nodeMap.put(item.getAttribute("id"), item.getAttribute("name"));
                            break;
                        case "startEvent":
                            nodeMap.put(item.getAttribute("id"), item.getAttribute("name"));
                            break;
                        case "subProcess":
                            nodeMap.putAll(getNode(item));
                            break;
                    }
                });
        return nodeMap;
    }

    /**
     * 循环获取流程节点信息（非通用）
     *
     * @param nodeList 流程节点列表
     * @param seqKey   流程节点key
     * @param seqMap   流程连接线
     * @param nodeMao  流程节点
     */
    private static void nodeList(ArrayList<HashMap<String, String>> nodeList, String seqKey, HashMap<String, String> seqMap, HashMap<String, String> nodeMao) {
        if (seqKey.contains(Constants.COMMA)) {
            String[] keyArr = seqKey.split(Constants.COMMA);
            for (int i = 0; i < keyArr.length; i++) {
                nodeList(nodeList, keyArr[i], seqMap, nodeMao);
            }
            return;
        }
        if (seqMap.get(seqKey) == null) {
            return;
        }
        if (seqKey.startsWith("Task") || seqKey.startsWith("StartEvent")) {
            HashMap<String, String> node = new HashMap<>();
            node.put("code", seqKey);
            node.put("name", nodeMao.get(seqKey));
            nodeList.add(node);
        }

        String req = seqMap.get(seqKey);
        seqMap.remove(seqKey);
        nodeMao.remove(seqKey);
        seqKey = req;
        nodeList(nodeList, seqKey, seqMap, nodeMao);
    }


    @Override
    public void createTaskAssignRule(BPM_TASK_RULE_VO reqVO) {
        // 校验参数
        validTaskAssignRuleOptions(reqVO.getF_TYPE(), reqVO.getF_OPTIONS());
        // 校验是否已经配置
        LambdaQueryWrapper<BPM_TASK_RULE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_TASK_RULE::getF_MODE, reqVO.getF_MODE())
                .eq(BPM_TASK_RULE::getF_TASK_KEY, reqVO.getF_TASK_KEY())
                .eq(BPM_TASK_RULE::getF_PROCESS_DEFINITION, PROCESS_DEFINITION_ID_NULL);
        BPM_TASK_RULE existRule = taskRuleMapper.selectOne(queryWrapper);
        if (existRule != null && StringUtils.isNotEmpty(existRule.getF_OPTIONS()) && existRule.getF_TYPE() != null) {
            throw new BusinessException(DataManagementExceptionEnum.TASK_ASSIGN_RULE_EXISTS, JudgeUtils.getLanguage(),
                    reqVO.getF_MODE(), reqVO.getF_TASK_KEY());
        }

        if (existRule == null) {
            // 存储
            BPM_TASK_RULE rule = BpmTaskAssignRuleConvert.INSTANCE.convert(reqVO);
            rule.setF_PROCESS_DEFINITION(PROCESS_DEFINITION_ID_NULL); // 只有流程模型，才允许新建
            taskRuleMapper.insert(rule);
        } else {
            existRule.setF_FROM(reqVO.getF_FROM());
            taskRuleMapper.updateById(existRule);
        }
    }

    @Override
    public void updateTaskAssignRule(BPM_TASK_RULE_VO reqVO) {
        // 校验参数
        validTaskAssignRuleOptions(reqVO.getF_TYPE(), reqVO.getF_OPTIONS());
        // 校验是否存在
        BPM_TASK_RULE existRule = taskRuleMapper.selectById(reqVO.getF_RULE());
        if (existRule == null) {
            throw new BusinessException(DataManagementExceptionEnum.TASK_ASSIGN_RULE_NOT_EXISTS);
        }
        // 只允许修改流程模型的规则
        if (!Objects.equals(PROCESS_DEFINITION_ID_NULL, existRule.getF_PROCESS_DEFINITION())) {
            throw new BusinessException(DataManagementExceptionEnum.TASK_UPDATE_FAIL_NOT_MODEL);
        }

        // 执行更新
        taskRuleMapper.updateById(BpmTaskAssignRuleConvert.INSTANCE.convert(reqVO));
    }


    @Override
    public boolean isTaskAssignRulesEquals(Long modelId, String processDefinitionId) {
        // 调用 VO 接口的原因是，过滤掉流程模型不需要的规则，保持和 copyTaskAssignRules 方法的一致性
        List<BPM_TASK_RULE_DTO> modelRules = getTaskAssignRuleList(modelId, null);
        List<BPM_TASK_RULE_DTO> processInstanceRules = getTaskAssignRuleList(null, processDefinitionId);
        if (modelRules.size() != processInstanceRules.size()) {
            return false;
        }

        // 遍历，匹配对应的规则
        Map<String, BPM_TASK_RULE_DTO> processInstanceRuleMap =
                CollectionToMapUtils.convertMap(processInstanceRules, BPM_TASK_RULE_DTO::getF_TASK_KEY);
        for (BPM_TASK_RULE_DTO modelRule : modelRules) {
            BPM_TASK_RULE_DTO processInstanceRule = processInstanceRuleMap.get(modelRule.getF_TASK_KEY());
            if (processInstanceRule == null) {
                return false;
            }
            if (!ObjectUtil.equals(modelRule.getF_TYPE(), processInstanceRule.getF_TYPE()) || !ObjectUtil.equal(
                    modelRule.getF_OPTIONS(), processInstanceRule.getF_OPTIONS())) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyTaskAssignRules(Long fromModelId, String toProcessDefinitionId) {
        List<BPM_TASK_RULE_DTO> rules = getTaskAssignRuleList(fromModelId, null);
        if (CollUtil.isEmpty(rules)) {
            return;
        }
        // 开始复制
        List<BPM_TASK_RULE> newRules = BpmTaskAssignRuleConvert.INSTANCE.convertList2(rules);
        newRules.forEach(rule -> rule.setF_PROCESS_DEFINITION(toProcessDefinitionId)
                .setF_RULE(null));
        this.saveBatch(newRules);
    }

    @Override
    public void checkTaskAssignRuleAllConfig(Long id) {
        // 一个用户任务都没配置，所以无需配置规则
        List<BPM_TASK_RULE_DTO> taskAssignRules = getTaskAssignRuleList(id, null);
        if (CollUtil.isEmpty(taskAssignRules)) {
            return;
        }
        // 校验未配置规则的任务
        taskAssignRules.forEach(rule -> {
            if (StringUtils.isEmpty(rule.getF_OPTIONS())) {
                throw new BusinessException(DataManagementExceptionEnum.MODEL_DEPLOY_FAIL_TASK_ASSIGN_RULE_NOT_CONFIG,
                        JudgeUtils.getLanguage(), rule.getTaskDefinitionName());
            }
        });
    }

    private void validTaskAssignRuleOptions(Integer type, String options) {
        // todo 自定义规则逻辑添加
//        new HashSet<>(JSONArray.parseArray(options,String.class));
//        if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.ROLE.getType())) {
//            roleApi.validRoleList(options);
//        } else if (ObjectUtils.equalsAny(type, BpmTaskAssignRuleTypeEnum.DEPT_MEMBER.getType(),
//            BpmTaskAssignRuleTypeEnum.DEPT_LEADER.getType())) {
//            deptApi.validateDeptList(options);
//        } else if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.POST.getType())) {
//            postApi.validPostList(options);
//        } else if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.USER.getType())) {
//            adminUserApi.validateUserList(options);
//        }else if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.SCRIPT.getType())) {
//            dictDataApi.validateDictDataList(DictTypeConstants.TASK_ASSIGN_SCRIPT,
//                CollectionUtils.convertSet(options, String::valueOf));
//        } else {
//            throw new IllegalArgumentException(format("未知的规则类型({})", type));
//        }
    }

    @Override
    public Set<Long> calculateTaskCandidateUsers(DelegateExecution execution) {
        BPM_TASK_RULE_DTO rule = getTaskRule(execution);
        return calculateTaskCandidateUsers(execution, rule);
    }

    @Override
    public List<BPM_TASK_RULE_DTO> getTaskAssignFormList(Long modelId, String processDefinitionId) {
        BpmnModelInstance model = modelService.getBpmnModel(modelId);
        List<BPM_TASK_RULE_DTO> ruleDtoList = getTaskAssignRuleListByModelId(modelId);
        Map<String, BPM_TASK_RULE_DTO> ruleMap = CollectionToMapUtils.convertMap(ruleDtoList, BPM_TASK_RULE_DTO::getF_TASK_KEY);
        List<BPM_TASK_RULE_DTO> taskAssignRuleList = getTaskAssignRuleList(modelId, processDefinitionId);
        Collection<StartEvent> startEvents = model.getModelElementsByType(StartEvent.class);
        List<StartEvent> startEventList = new ArrayList<>(startEvents);
        if (CollectionUtils.isNotEmpty(startEventList)) {
            if (ruleMap.get(startEventList.get(0).getId()) != null) {
                BPM_TASK_RULE_DTO bpmTaskRuleDto = ruleMap.get(startEventList.get(0).getId());
                bpmTaskRuleDto.setNum(WorkFlowConstants.startTaskNum)
                        .setTaskDefinitionName(startEventList.get(0).getName());
                taskAssignRuleList.add(bpmTaskRuleDto);
            } else {
                BPM_TASK_RULE_DTO bpmTaskRuleDto = new BPM_TASK_RULE_DTO();
                bpmTaskRuleDto.setNum(WorkFlowConstants.startTaskNum)
                        .setTaskDefinitionName(startEventList.get(0).getName())
                        .setF_TASK_KEY(startEventList.get(0).getId());
                taskAssignRuleList.add(bpmTaskRuleDto);
            }
        }
        Collection<EndEvent> endEvents = model.getModelElementsByType(EndEvent.class);
        List<EndEvent> endEventList = new ArrayList<>(endEvents);
        if (CollectionUtils.isNotEmpty(endEventList)) {
            if (ruleMap.get(endEventList.get(0).getId()) != null) {
                BPM_TASK_RULE_DTO bpmTaskRuleDto = ruleMap.get(endEventList.get(0).getId());
                bpmTaskRuleDto.setNum(WorkFlowConstants.endTaskNum)
                        .setTaskDefinitionName(endEventList.get(0).getName());
                taskAssignRuleList.add(bpmTaskRuleDto);
            } else {
                BPM_TASK_RULE_DTO bpmTaskRuleDto = new BPM_TASK_RULE_DTO();
                bpmTaskRuleDto.setNum(WorkFlowConstants.endTaskNum)
                        .setTaskDefinitionName(endEventList.get(0).getName())
                        .setF_TASK_KEY(endEventList.get(0).getId());
                taskAssignRuleList.add(bpmTaskRuleDto);
            }
        }
        List<BPM_TASK_RULE_DTO> collect = taskAssignRuleList.stream().sorted(Comparator.comparing(BPM_TASK_RULE_DTO::getNum)).collect(Collectors.toList());
        collect.forEach(bpmTaskRuleDto -> {
            if (StringUtils.isNotEmpty(bpmTaskRuleDto.getF_FROM())) {
                BPM_FROM_DTO form = bpmFormService.getForm(bpmTaskRuleDto.getF_FROM());
                if (form != null) {
                    bpmTaskRuleDto.setFromName(form.getF_NAME());
                    bpmTaskRuleDto.setFromType(form.getF_TYPE());
                }
            }
        });
        return collect;
    }

    @Override
    public void createTaskAssignForm(BPM_TASK_RULE_VO reqVO) {
        // 校验是否已经配置
        LambdaQueryWrapper<BPM_TASK_RULE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_TASK_RULE::getF_MODE, reqVO.getF_MODE())
                .eq(BPM_TASK_RULE::getF_TASK_KEY, reqVO.getF_TASK_KEY())
                .eq(BPM_TASK_RULE::getF_PROCESS_DEFINITION, PROCESS_DEFINITION_ID_NULL);
        BPM_TASK_RULE existRule = taskRuleMapper.selectOne(queryWrapper);
        if (existRule != null && StringUtils.isNotEmpty(existRule.getF_FROM())) {
            throw new BusinessException(DataManagementExceptionEnum.TASK_ASSIGN_FORM_EXISTS, JudgeUtils.getLanguage(),
                    reqVO.getF_MODE(), reqVO.getF_TASK_KEY());
        }
        if (existRule == null) {
            // 存储
            BPM_TASK_RULE rule = BpmTaskAssignRuleConvert.INSTANCE.convert(reqVO);
            rule.setF_PROCESS_DEFINITION(PROCESS_DEFINITION_ID_NULL); // 只有流程模型，才允许新建
            taskRuleMapper.insert(rule);
        } else {
            existRule.setF_FROM(reqVO.getF_FROM());
            taskRuleMapper.updateById(existRule);
        }
    }

    @Override
    public void updateTaskAssignForm(BPM_TASK_RULE_VO reqVO) {
        // 校验是否存在
        BPM_TASK_RULE existRule = taskRuleMapper.selectById(reqVO.getF_RULE());
        if (existRule == null) {
            throw new BusinessException(DataManagementExceptionEnum.TASK_ASSIGN_RULE_NOT_EXISTS);
        }
        // 只允许修改流程模型的规则
        if (!Objects.equals(PROCESS_DEFINITION_ID_NULL, existRule.getF_PROCESS_DEFINITION())) {
            throw new BusinessException(DataManagementExceptionEnum.TASK_UPDATE_FAIL_NOT_MODEL);
        }

        // 执行更新
        taskRuleMapper.updateById(BpmTaskAssignRuleConvert.INSTANCE.convert(reqVO));
    }

    @VisibleForTesting
    BPM_TASK_RULE_DTO getTaskRule(DelegateExecution execution) {
        List<BPM_TASK_RULE_DTO> taskRules = getTaskAssignRuleListByProcessDefinitionId(
                execution.getProcessDefinitionId(), execution.getCurrentActivityId());
        if (CollUtil.isEmpty(taskRules)) {
            throw new RuntimeException(format(I18nUtils.getMessage("PROCESS_TASK_NO_MATCHING_RULE_FOUND"),
                    execution.getId(), execution.getProcessDefinitionId(), execution.getCurrentActivityId()));
        }
        if (taskRules.size() > 1) {
            throw new RuntimeException(format(I18nUtils.getMessage("PROCESS_TASK_TOO_MANY_RULES_FOUND"),
                    execution.getId(), execution.getProcessDefinitionId(), execution.getCurrentActivityId()));
        }
        return taskRules.get(0);
    }

    @VisibleForTesting
    Set<Long> calculateTaskCandidateUsers(DelegateExecution execution, BPM_TASK_RULE_DTO rule) {
        //todo 待完善
        Set<Long> assigneeUserIds = getRuleUsers(rule);

//        // 移除被禁用的用户
//        removeDisableUsers(assigneeUserIds);
        // 如果候选人为空，抛出异常
        if (CollectionUtils.isEmpty(assigneeUserIds)) {
            log.error("[calculateTaskCandidateUsers][流程任务({}/{}/{}) 任务规则({}) 找不到候选人]", execution.getId(),
                    execution.getProcessDefinitionId(), execution.getCurrentActivityId(), rule.getF_OPTIONS());
            throw new BusinessException(DataManagementExceptionEnum.TASK_CREATE_FAIL_NO_CANDIDATE_USER);
        }
        return assigneeUserIds;
    }

    public Set<Long> getRuleUsers(BPM_TASK_RULE_DTO rule) {
        Set<Long> assigneeUserIds = null;
        if (Objects.equals(BpmTaskAssignRuleTypeEnum.ROLE.getType(), rule.getF_TYPE())) {
            assigneeUserIds = calculateTaskCandidateUsers(rule);
        } else if (Objects.equals(BpmTaskAssignRuleTypeEnum.USER.getType(), rule.getF_TYPE())) {
            assigneeUserIds = Arrays.stream(rule.getF_OPTIONS().split(Constants.COMMA))
                    .filter(StringUtils::isNotEmpty).map(Long::valueOf).collect(Collectors.toSet());
        } else if (Objects.equals(BpmTaskAssignRuleTypeEnum.HIERARCHY.getType(), rule.getF_TYPE())) {
            assigneeUserIds = calculateTaskCandidateUsers(rule);
        }
        return assigneeUserIds;
    }

    private Set<Long> calculateTaskCandidateUsers(BPM_TASK_RULE_DTO rule) {
        R<Set<Long>> byIds = remoteUserService.findByIds(rule.getF_OPTIONS(), rule.getF_TYPE());
        return byIds.getData();
    }
//
//    private Set<Long> calculateTaskCandidateUsersByDeptMember(BPM_TASK_RULE_DTO rule) {
//        List<AdminUserRespDTO> users = adminUserApi.getUserListByDeptIds(rule.getOptions());
//        return convertSet(users, AdminUserRespDTO::getId);
//    }
//
//    private Set<Long> calculateTaskCandidateUsersByDeptLeader(BPM_TASK_RULE_DTO rule) {
//        List<DeptRespDTO> depts = deptApi.getDeptList(rule.getOptions());
//        return convertSet(depts, DeptRespDTO::getLeaderUserId);
//    }
//
//    private Set<Long> calculateTaskCandidateUsersByPost(BPM_TASK_RULE_DTO rule) {
//        List<AdminUserRespDTO> users = adminUserApi.getUsersByPostIds(rule.getOptions());
//        return convertSet(users, AdminUserRespDTO::getId);
//    }
//
//    private Set<Long> calculateTaskCandidateUsersByUser(BPM_TASK_RULE_DTO rule) {
//        return rule.getOptions();
//    }
//
//    private Set<Long> calculateTaskCandidateUsersByUserGroup(BPM_TASK_RULE_DTO rule) {
//        List<BpmUserGroupDO> userGroups = userGroupService.getUserGroupList(rule.getOptions());
//        Set<Long> userIds = new HashSet<>();
//        userGroups.forEach(group -> userIds.addAll(group.getMemberUserIds()));
//        return userIds;
//    }
//
//    private Set<Long> calculateTaskCandidateUsersByScript(DelegateExecution execution, BPM_TASK_RULE_DTO rule) {
//        // 获得对应的脚本
//        List<BpmTaskAssignScript> scripts = new ArrayList<>(rule.getOptions().size());
//        rule.getOptions().forEach(id -> {
//            BpmTaskAssignScript script = scriptMap.get(id);
//            if (script == null) {
//                throw exception(TASK_ASSIGN_SCRIPT_NOT_EXISTS, id);
//            }
//            scripts.add(script);
//        });
//        // 逐个计算任务
//        Set<Long> userIds = new HashSet<>();
//        scripts.forEach(script -> CollUtil.addAll(userIds, script.calculateTaskCandidateUsers(execution)));
//        return userIds;
//    }

//    @VisibleForTesting
//    void removeDisableUsers(Set<Long> assigneeUserIds) {
//        if (CollUtil.isEmpty(assigneeUserIds)) {
//            return;
//        }
//        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(assigneeUserIds);
//        assigneeUserIds.removeIf(id -> {
//            AdminUserRespDTO user = userMap.get(id);
//            return user == null || !CommonStatusEnum.ENABLE.getStatus().equals(user.getStatus());
//        });
//    }

}
