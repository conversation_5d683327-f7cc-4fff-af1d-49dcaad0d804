package com.yingfei.dataManagement.service.chart.impl;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.util.DateUtils;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.entity.dto.SGRP_DSC_DTO;
import com.yingfei.entity.dto.chart.StreamTrendInfCatchDTO;
import com.yingfei.entity.dto.chart.StreamTrendInfCatchData2DTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ExportStreamTrendUtil {

    public static void exportExcel(HttpServletResponse response, List<StreamTrendInfCatchData2DTO> streamTrendInfList, String fileName, Map<String, String> fillInfoMap, byte[] imageBytes) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(I18nUtils.getMessage("STREAM_TREND_EXPORT"));
        // 创建表头样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        // 创建标题样式
        CellStyle titleStyle = createTitleStyle(workbook);
        // 创建数据样式
        CellStyle fillHeaderStyle = createfillHeadertyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);

        // 填充 Excel 前几行信息
        fillHeaderInfo(sheet, fillInfoMap, titleStyle, fillHeaderStyle);
        int currentRowIndex = getHeaderRowIndex(fillInfoMap);

        final int maxFillDynamicSize = StreamTrendInfCatchData2DTO.getMaxGroupByDataSize(streamTrendInfList);

        for (StreamTrendInfCatchData2DTO trendList : streamTrendInfList) {
            // 获取部分固定表头
            List<String> fixedHeadersPart1 = getFixedHeadersPart1();
            // 获取剩余固定表头
            List<String> fixedHeadersPart2 = getFixedHeadersPart2();
            // 获取动态表头
            Set<String> dynamicHeaders = getDynamicHeaders(trendList.getData());

            // 合并固定表头和动态表头
            // 合并部分固定表头、动态表头和剩余固定表头
            List<String> allHeaders = new ArrayList<>(fixedHeadersPart1);
            allHeaders.addAll(dynamicHeaders);
            allHeaders.addAll(fixedHeadersPart2);

            // 创建表头，从当前行开始
            Row headerRow = sheet.createRow(currentRowIndex);
            Map<String, Integer> headerIndexMap = new HashMap<>(allHeaders.size());
            for (int i = 0; i < allHeaders.size(); i++) {
                String header = allHeaders.get(i);
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(header);
                cell.setCellStyle(headerStyle); // 设置表头样式
                headerIndexMap.put(header, i);
            }

            // 填充数据，从表头的下一行开始
            int rowIndex = currentRowIndex + 1;
            for (StreamTrendInfCatchDTO trend : trendList.getData()) {
                Row dataRow = sheet.createRow(rowIndex);
                // 填充固定字段数据
                fillFixedData(dataRow, trend, headerIndexMap,dataStyle);
                // 填充动态字段数据
                fillDynamicData(dataRow, trend.getSgrpDscList(), headerIndexMap,dataStyle);
                rowIndex++;
            }
            // 隐藏没有值的列
            hideEmptyColumns(sheet, currentRowIndex, rowIndex - 1, allHeaders.size());
            // 自定义条件合并列值单元格，这里以 "子组ID" 列为例

            int offset = 0;
            if(fillInfoMap.get(I18nUtils.getMessage("DATA_STATISTICS_METHOD")).contains(I18nUtils.getMessage("OFFSET"))){
                Pattern pattern = Pattern.compile(I18nUtils.getMessage("GROUP_COUNT")+":(\\d+)\\s+"+I18nUtils.getMessage("OFFSET")+":(\\d+)");
                Matcher matcher = pattern.matcher(fillInfoMap.get(I18nUtils.getMessage("DATA_STATISTICS_METHOD")));
                if (matcher.find()) {
                    offset = Integer.parseInt(matcher.group(2));
                }
            }
            if(offset>0){
                mergeCellsByCondition(sheet, headerIndexMap, I18nUtils.getMessage("CPK"),I18nUtils.getMessage("SUBGROUP_ID"),-1);
                mergeCellsByCondition(sheet, headerIndexMap, I18nUtils.getMessage("CPK_TARGET_VALUE_1"),I18nUtils.getMessage("SUBGROUP_ID"),-1);
                mergeCellsByCondition(sheet, headerIndexMap, I18nUtils.getMessage("CPK_TARGET_VALUE_2"),I18nUtils.getMessage("SUBGROUP_ID"),-1);
            }else{
                mergeCellsByConditionWithGroupValues(sheet, headerIndexMap, I18nUtils.getMessage("CPK"), I18nUtils.getMessage("SUBGROUP_ID"), trendList.getGroupByData());
                mergeCellsByConditionWithGroupValues(sheet, headerIndexMap, I18nUtils.getMessage("CPK_TARGET_VALUE_1"), I18nUtils.getMessage("SUBGROUP_ID"), trendList.getGroupByData());
                mergeCellsByConditionWithGroupValues(sheet, headerIndexMap, I18nUtils.getMessage("CPK_TARGET_VALUE_2"), I18nUtils.getMessage("SUBGROUP_ID"), trendList.getGroupByData());
            }

            // 更新当前行号，为下一个表格留出一行空白
            currentRowIndex = rowIndex + 1;
        }

        // 在表格后面添加图片
        if (imageBytes != null) {
            int imageRowIndex = currentRowIndex;
            int imageColIndex = 0; // 图片从第 0 列开始
            insertImage(sheet, workbook, imageRowIndex, imageColIndex, imageBytes);
        }

        // 自动调整列宽并设置最小列宽
        int minColumnWidth = 12 * 256; // 最小列宽为 10 字符
        int maxColumnWidth = 50 * 256; // 最大列宽，256 是 Apache POI 中一个字符的宽度单位
        for (int i = 0; i < (getFixedHeadersPart1().size()+getFixedHeadersPart2().size()) + maxFillDynamicSize; i++) {
            sheet.autoSizeColumn(i);
            // 获取当前列宽
            int columnWidth = sheet.getColumnWidth(i);
            // 设置最小列宽
            if (columnWidth < minColumnWidth) {
                columnWidth = minColumnWidth;
            }
            // 设置最大列宽，避免列宽过大
            if (columnWidth > maxColumnWidth) {
                columnWidth = maxColumnWidth;
            }
            // 设置最终列宽
            sheet.setColumnWidth(i, columnWidth);
        }
        // 输出工作簿
        try (OutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
            outputStream.flush();
        }
    }
    /**
     * 填充 Excel 前几行信息
     *
     * @param sheet       工作表对象
     * @param fillInfoMap 填充信息映射
     * @param titleStyle  标题样式
     * @param dataStyle   数据样式
     */
    private static void fillHeaderInfo(Sheet sheet, Map<String, String> fillInfoMap, CellStyle titleStyle, CellStyle dataStyle) {
        // 检查 fillInfoMap 是否为空，若为空则直接返回
        if (fillInfoMap == null || fillInfoMap.isEmpty()) {
            return;
        }

        // 检查样式是否为空，若为空则创建默认样式
        if (titleStyle == null) {
            titleStyle = sheet.getWorkbook().createCellStyle();
        }
        if (dataStyle == null) {
            dataStyle = sheet.getWorkbook().createCellStyle();
        }

        // 添加标题行
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(I18nUtils.getMessage("REAL_TIME_CAPABILITY_ANALYSIS"));
        titleCell.setCellStyle(titleStyle);
        // 合并标题行所有列，列数为 fillInfoMap 大小的两倍
//        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, fillInfoMap.size() * 2 - 1));

        int rowIndex = 1;
        for (Map.Entry<String, String> entry : fillInfoMap.entrySet()) {
            Row row = sheet.createRow(rowIndex);
            // 第一列显示 key
            Cell keyCell = row.createCell(0);
            keyCell.setCellValue(entry.getKey());
            keyCell.setCellStyle(dataStyle);

            // 第二列显示 value 并合并 3 列单元格
            Cell valueCell = row.createCell(1);
            valueCell.setCellValue(entry.getValue());
            valueCell.setCellStyle(dataStyle);
//            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, 3));

            rowIndex++;
        }



    }



    // 获取 "工单" 之前（包含 "工单"）的固定表头
    private static List<String> getFixedHeadersPart1() {
        List<String> headers = new ArrayList<>();
        headers.add(I18nUtils.getMessage("SUBGROUP_ID"));
        headers.add(I18nUtils.getMessage("SUBGROUP_TIME"));
        headers.add(I18nUtils.getMessage("PRODUCT"));
        headers.add(I18nUtils.getMessage("VERSION"));
        headers.add(I18nUtils.getMessage("PROCESS"));
        headers.add(I18nUtils.getMessage("TEST"));
        headers.add(I18nUtils.getMessage("BATCH"));
        headers.add(I18nUtils.getMessage("SHIFT"));
        headers.add(I18nUtils.getMessage("WORK_ORDER"));
        return headers;
    }

    // 获取 "测试序号" 及之后的固定表头
    private static List<String> getFixedHeadersPart2() {
        List<String> headers = new ArrayList<>();
        headers.add(I18nUtils.getMessage("TEST_SEQUENCE_NUMBER"));
        headers.add(I18nUtils.getMessage("CP"));
        headers.add(I18nUtils.getMessage("CPK_TARGET_VALUE_1"));
        headers.add(I18nUtils.getMessage("CPK_TARGET_VALUE_2"));
        headers.add(I18nUtils.getMessage("CPK"));
        return headers;
    }

    private static Set<String> getDynamicHeaders(List<StreamTrendInfCatchDTO> trendList) {
        Set<String> dynamicHeaders = new LinkedHashSet<>();
        for (StreamTrendInfCatchDTO trend : trendList) {
            if (trend.getSgrpDscList() != null) {
                for (SGRP_DSC_DTO dto : trend.getSgrpDscList()) {
                    dynamicHeaders.add(dto.getDsgpName());
                }
            }
        }
        return dynamicHeaders;
    }

    private static void fillFixedData(Row dataRow, StreamTrendInfCatchDTO trend, Map<String, Integer> headerIndexMap, CellStyle dataStyle) {
        // 示例填充，根据实际情况修改
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("SUBGROUP_ID"), trend.getF_SGRP());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("SUBGROUP_TIME"), trend.getF_SGTM());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("PRODUCT"), trend.getPartName());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("VERSION"), trend.getRevName());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("PROCESS"), trend.getPrcsName());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("TEST"), trend.getTestName());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("BATCH"), trend.getLotName());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("SHIFT"), trend.getShiftName());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("WORK_ORDER"), trend.getJobName());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("TEST_SEQUENCE_NUMBER"), trend.getTestNo());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("ACTUAL_VALUE"), trend.getTestVal());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("CPK_TARGET_VALUE_1"), trend.getF_CPTAR());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("CPK_TARGET_VALUE_2"), trend.getF_CPKTAR());
        setCellValue(dataRow, headerIndexMap,dataStyle, I18nUtils.getMessage("CPK"), trend.getF_CPK());
    }

    private static void fillDynamicData(Row dataRow, List<SGRP_DSC_DTO> sgrpDscList, Map<String, Integer> headerIndexMap,CellStyle dataStyle) {
        if (sgrpDscList != null) {
            for (SGRP_DSC_DTO dto : sgrpDscList) {
                Integer colIndex = headerIndexMap.get(dto.getDsgpName());
                if (colIndex != null && dto.getDescName() != null) {
                    Cell cell = dataRow.createCell(colIndex);
                    cell.setCellStyle(dataStyle);
                    cell.setCellValue(dto.getDescName());
                }
            }
        }
    }

    private static void insertImage(Sheet sheet, Workbook workbook, int rowIndex, Integer colIndex, byte[] imageBytes) {
        if (imageBytes != null && colIndex != null) {
            try {
                int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
                CreationHelper helper = workbook.getCreationHelper();
                Drawing<?> drawing = sheet.createDrawingPatriarch();
                ClientAnchor anchor = helper.createClientAnchor();
                anchor.setCol1(colIndex);
                anchor.setRow1(rowIndex);
                Picture pict = drawing.createPicture(anchor, pictureIdx);
                pict.resize();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static void setCellValue(Row dataRow, Map<String, Integer> headerIndexMap,CellStyle dataStyle, String header, Object value) {
        Integer colIndex = headerIndexMap.get(header);
        if (colIndex != null && value != null) {
            Cell cell = dataRow.createCell(colIndex);
            cell.setCellStyle(dataStyle);
            if (value instanceof String) {
                cell.setCellValue((String) value);
            } else if (value instanceof Integer) {
                cell.setCellValue((Integer) value);
            } else if (value instanceof Double) {
                cell.setCellValue((Double) value);
            } else if (value instanceof Date) {
                cell.setCellValue(DateUtils.format((Date) value, DatePattern.NORM_DATETIME_PATTERN));
            } else if (value instanceof Long) {
                cell.setCellValue(value +"");
            }
            // 其他类型可按需添加
        }
    }

    private static int getHeaderRowIndex(Map<String, String> fillInfoMap) {
        return fillInfoMap.size()+1;
    }

    /**
     * 创建表头样式
     *
     * @param workbook 工作簿对象
     * @return 表头样式对象
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        // 设置背景颜色
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("Arial");
        font.setFontHeightInPoints((short) 10);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());

        return style;
    }

    /**
     * 创建标题样式
     *
     * @param workbook 工作簿对象
     * @return 标题样式对象
     */
    private static CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
        font.setBold(true);
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setFontHeightInPoints((short) 16);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 创建数据样式
     *
     * @param workbook 工作簿对象
     * @return 数据样式对象
     */
    private static CellStyle createfillHeadertyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }
    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 隐藏没有值的列
     * @param sheet 工作表
     * @param headerRowIndex 表头行索引
     * @param lastDataRowIndex 最后一行数据的索引
     * @param columnCount 列数
     */
    private static void hideEmptyColumns(Sheet sheet, int headerRowIndex, int lastDataRowIndex, int columnCount) {
        for (int colIndex = 0; colIndex < columnCount; colIndex++) {
            boolean hasValue = false;
            // 从表头的下一行开始检查，到最后一行数据
            for (int rowIndex = headerRowIndex + 1; rowIndex <= lastDataRowIndex; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    Cell cell = row.getCell(colIndex);
                    if (cell != null && cell.getCellType() != CellType.BLANK) {
                        hasValue = true;
                        break;
                    }
                }
            }
            // 如果整列都没有值，则隐藏该列
            if (!hasValue) {
                sheet.setColumnHidden(colIndex, true);
            }
        }
    }

    /**
     * 自定义条件合并指定列的相邻且值相同的单元格，支持分组列名和分组数量
     * @param sheet 工作表
     * @param headerIndexMap 表头索引映射
     * @param mergeColumnName 指定要合并的列名
     * @param groupColumnName 分组列名，可为空
     * @param groupSize 分组数量，<=0 表示不限制
     */
    private static void mergeCellsByCondition(Sheet sheet, Map<String, Integer> headerIndexMap, String mergeColumnName, String groupColumnName, int groupSize) {
        Integer mergeColIndex = headerIndexMap.get(mergeColumnName);
        if (mergeColIndex == null) {
            return;
        }

        Integer groupColIndex = null;
        if (groupColumnName != null) {
            groupColIndex = headerIndexMap.get(groupColumnName);
            if (groupColIndex == null) {
                return;
            }
        }

        int lastRowNum = sheet.getLastRowNum();
        int startRow = getHeaderRowIndex(new HashMap<>()) + 1; // 从表头下一行开始
        String prevGroupValue = null;
        String prevMergeValue = null;
        int groupStartRow = startRow;
        int currentGroupCount = 0;

        for (int rowIndex = startRow; rowIndex <= lastRowNum; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                continue;
            }

            String currentGroupValue = null;
            if (groupColIndex != null) {
                Cell groupCell = row.getCell(groupColIndex);
                currentGroupValue = getCellValueAsString(groupCell);
            }

            Cell mergeCell = row.getCell(mergeColIndex);
            String currentMergeValue = getCellValueAsString(mergeCell);

            if (prevGroupValue == null) {
                prevGroupValue = currentGroupValue;
                prevMergeValue = currentMergeValue;
                currentGroupCount = 1;
            } else if (groupColIndex != null && !prevGroupValue.equals(currentGroupValue)) {
                // 分组列存在且分组变化，合并上一组的单元格
                mergeGroupCells(sheet, mergeColIndex, groupStartRow, rowIndex - 1, prevMergeValue, groupSize);
                groupStartRow = rowIndex;
                prevGroupValue = currentGroupValue;
                prevMergeValue = currentMergeValue;
                currentGroupCount = 1;
            } else {
                currentGroupCount++;
                boolean shouldMerge = !prevMergeValue.equals(currentMergeValue);
                if (groupSize > 0) {
                    shouldMerge = shouldMerge || currentGroupCount > groupSize;
                }

                if (shouldMerge) {
                    int endRow = groupSize > 0 && currentGroupCount > groupSize ? rowIndex - 1 : rowIndex;
                    mergeGroupCells(sheet, mergeColIndex, groupStartRow, endRow, prevMergeValue, groupSize);
                    groupStartRow = groupSize > 0 && currentGroupCount > groupSize ? rowIndex : rowIndex;
                    prevMergeValue = currentMergeValue;
                    currentGroupCount = groupSize > 0 && currentGroupCount > groupSize ? 1 : currentGroupCount;
                }
            }
        }

        // 处理最后一组相同值的单元格
        if (lastRowNum >= groupStartRow) {
            mergeGroupCells(sheet, mergeColIndex, groupStartRow, lastRowNum, prevMergeValue, groupSize);
        }
    }

    /**
     * 合并指定分组内的单元格
     * @param sheet 工作表
     * @param mergeColIndex 要合并的列索引
     * @param startRow 起始行
     * @param endRow 结束行
     * @param prevMergeValue 上一个合并值
     * @param groupSize 分组数量，<=0 表示不限制
     */
    private static void mergeGroupCells(Sheet sheet, int mergeColIndex, int startRow, int endRow, String prevMergeValue, int groupSize) {
        int currentStart = startRow;
        int currentCount = 0;
        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                continue;
            }
            Cell cell = row.getCell(mergeColIndex);
            String currentValue = getCellValueAsString(cell);
            currentCount++;

            boolean shouldMerge = !prevMergeValue.equals(currentValue);
            if (groupSize > 0) {
                shouldMerge = shouldMerge || currentCount > groupSize;
            }

            if (shouldMerge) {
                // 检查合并区域是否至少包含两个单元格
                if (rowIndex - 1 > currentStart) {
                    sheet.addMergedRegion(new CellRangeAddress(currentStart, rowIndex - 1, mergeColIndex, mergeColIndex));
                }
                currentStart = rowIndex;
                prevMergeValue = currentValue;
                currentCount = groupSize > 0 && currentCount > groupSize ? 1 : 1;
            }
        }
        // 处理最后一组，检查合并区域是否至少包含两个单元格
        if (endRow > currentStart) {
            sheet.addMergedRegion(new CellRangeAddress(currentStart, endRow, mergeColIndex, mergeColIndex));
        }
    }



    /**
     * 检查合并区域是否已存在
     * @param sheet 工作表
     * @param newRange 要检查的合并区域
     * @return 如果区域已存在返回 true，否则返回 false
     */
    private static boolean isMergedRegionExists(Sheet sheet, CellRangeAddress newRange) {
        int numMergedRegions = sheet.getNumMergedRegions();
        for (int i = 0; i < numMergedRegions; i++) {
            CellRangeAddress existingRange = sheet.getMergedRegion(i);
            if (existingRange.equals(newRange)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 获取单元格的值并转换为字符串
     * @param cell 单元格
     * @return 单元格的值
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


    /**
     * 按分组精确限制合并范围，避免跨分组合并
     * @param sheet 工作表
     * @param headerIndexMap 表头索引映射
     * @param mergeColumnName 合并列名（如"cpk"）
     * @param groupColumnName 分组列名（如"子组ID"）
     * @param groupBy 分组范围集合，每个子列表代表一个独立分组
     */
    private static void mergeCellsByConditionWithGroupValues(Sheet sheet, Map<String, Integer> headerIndexMap,
                                                             String mergeColumnName, String groupColumnName,
                                                             List<List<String>> groupBy) {
        Integer mergeColIndex = headerIndexMap.get(mergeColumnName);
        Integer groupColIndex = headerIndexMap.get(groupColumnName);
        if (mergeColIndex == null || groupColIndex == null) {
            return;
        }

        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum <= 0) {
            return;
        }

        int startRow = getHeaderRowIndex(new HashMap<>()) + 1; // 数据起始行

        // 对每个分组独立处理合并逻辑
        for (List<String> group : groupBy) {
            if (group == null || group.isEmpty()) {
                continue;
            }

            // 转换为Set便于快速查询
            Set<String> groupValues = new HashSet<>(group);

            // 获取当前分组对应的所有行索引
            List<Integer> groupRowIndices = getGroupRowIndices(sheet, groupColIndex, groupValues, startRow, lastRowNum);
            if (groupRowIndices.isEmpty()) {
                continue;
            }

            // 在当前分组的行范围内执行合并
            mergeWithinGroup(sheet, headerIndexMap, mergeColumnName,
                    groupRowIndices, mergeColIndex);
        }
    }

    /**
     * 获取分组对应的所有行索引
     */
    private static List<Integer> getGroupRowIndices(Sheet sheet, int groupColIndex, Set<String> groupValues,
                                                    int startRow, int endRow) {
        List<Integer> rowIndices = new ArrayList<>();
        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                continue;
            }

            Cell groupCell = row.getCell(groupColIndex);
            String groupValue = getCellValueAsString(groupCell);

            if (groupValues.contains(groupValue)) {
                rowIndices.add(rowIndex);
            }
        }
        return rowIndices;
    }

    /**
     * 在分组行范围内执行合并
     */
    private static void mergeWithinGroup(Sheet sheet, Map<String, Integer> headerIndexMap,
                                         String mergeColumnName, List<Integer> groupRowIndices,
                                         int mergeColIndex) {
        if (groupRowIndices.isEmpty()) {
            return;
        }

        // 按行索引排序
        Collections.sort(groupRowIndices);

        String currentMergeValue = null;
        int mergeStartRow = -1;

        for (int rowIndex : groupRowIndices) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                continue;
            }

            Cell mergeCell = row.getCell(mergeColIndex);
            String mergeValue = getCellValueAsString(mergeCell);

            if (mergeStartRow == -1) {
                mergeStartRow = rowIndex;
                currentMergeValue = mergeValue;
                continue;
            }

            // 合并值变化时执行合并
            if (!currentMergeValue.equals(mergeValue)) {
                mergeRegion(sheet, mergeColIndex, mergeStartRow, rowIndex - 1, currentMergeValue);
                mergeStartRow = rowIndex;
                currentMergeValue = mergeValue;
            }
        }

        // 处理分组内最后一组数据
        if (mergeStartRow != -1) {
            int lastRow = groupRowIndices.get(groupRowIndices.size() - 1);
            mergeRegion(sheet, mergeColIndex, mergeStartRow, lastRow, currentMergeValue);
        }
    }

    /**
     * 合并区域（带冲突检测）
     */
    private static void mergeRegion(Sheet sheet, int mergeColIndex, int startRow, int endRow, String value) {
        if (startRow >= endRow) {
            return;
        }
        CellRangeAddress newRegion = new CellRangeAddress(startRow, endRow, mergeColIndex, mergeColIndex);
        // 检查新的合并区域是否与已存在的合并区域重叠
        if (!isMergedRegionExists(sheet, newRegion)) {
            sheet.addMergedRegion(newRegion);
        }
    }


}

