package com.yingfei.dataManagement.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.exception.enums.SystemExceptionEnum;
import com.yingfei.common.core.utils.CollectionToMapUtils;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.SGRP_INFMapper;
import com.yingfei.dataManagement.mapper.SGRP_INF_UNFINISHEDMapper;
import com.yingfei.dataManagement.mapper.convert.SubgroupDataConvert;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.DataReportService;
import com.yingfei.dataManagement.service.manufacturing.INSPECTION_PLAN_INFService;
import com.yingfei.dataManagement.service.manufacturing.MANUFACTURING_PROCESS_INFService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.enums.HIERARCHY_INFTypeEnum;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.vo.*;
import com.yingfei.mq.api.RemoteMqService;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 针对表【SGRP_INF(子组主信息表)】的数据库操作Service实现
 * @createDate 2024-06-13 15:02:05
 */
@SuppressWarnings("AlibabaTransactionMustHaveRollback")
@Slf4j
@Service
public class SGRP_INFServiceImpl extends ServiceImpl<SGRP_INFMapper, SGRP_INF>
        implements SGRP_INFService {

    @Resource
    private RemoteMqService remoteMqService;
    @Resource
    private DEF_DATService defDatService;
    @Resource
    private DEF_GRPService defGrpService;
    @Resource
    private PART_INFService partInfService;
    @Resource
    private PRCS_INFService prcsInfService;
    @Resource
    private PART_REVService partRevService;
    @Resource
    private LOT_INFService lotInfService;
    @Resource
    private JOB_DATService jobDatService;
    @Resource
    private JOB_GRPService jobGrpService;
    @Resource
    private SHIFT_DATService shiftDatService;
    @Resource
    private SHIFT_GRPService shiftGrpService;
    @Resource
    private DESC_DATService descDatService;
    @Resource
    private DESC_GRPService descGrpService;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private SGRP_INF_AService sgrpInfAService;
    @Resource
    private SGRP_DSCService sgrpDscService;
    @Resource
    private SGRP_DSC_AService sgrpDscAService;
    @Resource
    private SGRP_VALService sgrpValService;
    @Resource
    private SGRP_VAL_AService sgrpValAService;
    @Resource
    private SGRP_CMTService sgrpCmtService;
    @Resource
    private SGRP_CMT_AService sgrpCmtAService;
    @Resource
    private EVNT_INFService evntInfService;
    @Resource
    private RedisService redisService;
    @Resource
    private INSPECTION_PLAN_INFService inspectionPlanInfService;
    @Resource
    private MANUFACTURING_PROCESS_INFService manufacturingProcessInfService;
    @Resource
    private DataReportService dataReportService;
    @Resource
    private TEMP_TABLE_INFService tempTableInfService;
    @Resource
    private SGRP_INF_UNFINISHEDMapper sgrpInfUnfinishedMapper;
    @Resource
    private ChartCommonService chartCommonService;

    @Override
    public long getTotal(SubgroupDataVO subgroupDataVO) {
        return 0;
    }

    @Override
    public List<SubgroupDataDTO> getList(SubgroupDataVO subgroupDataVO) {
        return null;
    }

    @Override
    public List<SubgroupDataDTO> getList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        if (subgroupDataSelectionDTO.getTotalNum() == null) {
            subgroupDataSelectionDTO.setTotalNum(1000);
        }
        return baseMapper.getSubgroupDataDTOList(subgroupDataSelectionDTO);
    }

    @Override
    public void add(SubgroupDataVO subgroupDataVO) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        List<SGRP_INF> sgrpInfList = baseMapper.selectBatchIds(ids);
        /*根据id列表修改对应的删除状态*/
        LambdaUpdateWrapper<SGRP_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SGRP_INF::getF_SGRP, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType())
                .set(SGRP_INF::getF_DEL, YesOrNoEnum.YES.getType()).set(SGRP_INF::getF_EDUE,SecurityUtils.getUserId());
        baseMapper.update(null, updateWrapper);

        LambdaUpdateWrapper<SGRP_INF_A> updateWrapperA = new LambdaUpdateWrapper<>();
        updateWrapperA.in(SGRP_INF_A::getF_SGRP, ids).eq(SGRP_INF_A::getF_DEL, DelFlagEnum.USE.getType())
                .set(SGRP_INF_A::getF_DEL, YesOrNoEnum.YES.getType()).set(SGRP_INF_A::getF_EDUE,SecurityUtils.getUserId());
        sgrpInfAService.update(null, updateWrapperA);

        /*删除对应的过程事件*/
        LambdaQueryWrapper<EVNT_INF> evntWrapper = new LambdaQueryWrapper<>();
        evntWrapper.in(EVNT_INF::getF_SGRP, ids).eq(EVNT_INF::getF_DEL, DelFlagEnum.USE.getType());
        List<EVNT_INF> infList = evntInfService.list(evntWrapper);
        if (CollectionUtils.isNotEmpty(infList)) {
            evntInfService.del(infList.stream().map(EVNT_INF::getF_EVNT).collect(Collectors.toList()));
        }
        /*删除子组待完成子计划记录表*/
        Set<String> collect = sgrpInfList.stream().map(SGRP_INF::getF_SAMPLE_ID).collect(Collectors.toSet());
        LambdaQueryWrapper<SGRP_INF_UNFINISHED> unfinishedWrapper = new LambdaQueryWrapper<>();
        unfinishedWrapper.in(SGRP_INF_UNFINISHED::getF_SAMPLE_ID, collect);
        sgrpInfUnfinishedMapper.delete(unfinishedWrapper);
    }

    @Override
    public void checkParam(SubgroupDataVO subgroupDataVO) {

    }

    @Override
    public List<SubgroupDataDTO> getFilterList(SubgroupFilterVO subgroupFilterVO) {
        return baseMapper.getFilterList(subgroupFilterVO);
    }

    @Override
    @Transactional
    public List<SubgroupDataDTO> getSubgroupDataDTOList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO.setDbType(InitConfig.getDriverType());
        if (subgroupDataSelectionDTO.getTotalNum() == null) {
            subgroupDataSelectionDTO.setTotalNum(1000);
        }
        getDescList(subgroupDataSelectionDTO);
        List<SubgroupDataDTO> subgroupDataDTOList = baseMapper.getSubgroupDataDTOList(subgroupDataSelectionDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) return subgroupDataDTOList;
        subgroupDataDTOList = structure(subgroupDataDTOList, subgroupDataSelectionDTO, false);
        cleanTemp(subgroupDataSelectionDTO);
        return subgroupDataDTOList;
    }

    /**
     * 清除临时表数据
     *
     * @param subgroupDataSelectionDTO
     */
    private void cleanTemp(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        if (StringUtils.isNotEmpty(subgroupDataSelectionDTO.getTempIdentify())) {
            tempTableInfService.delete(subgroupDataSelectionDTO.getTempIdentify());
        }
    }








    /**
     * 构造子组内容
     *
     * @param subgroupDataDTOList      子组列表
     * @param subgroupDataSelectionDTO 查询条件
     * @param isPage                   是否分页
     */
    private List<SubgroupDataDTO> structure(List<SubgroupDataDTO> subgroupDataDTOList, SubgroupDataSelectionDTO subgroupDataSelectionDTO, boolean isPage) {
        Map<Long, SubgroupDataDTO> subgroupDataDTOMap = CollectionToMapUtils.convertMap(subgroupDataDTOList, SubgroupDataDTO::getF_SGRP);

        subgroupDataSelectionDTO.setSgrpList(null);
        if (isPage && subgroupDataSelectionDTO.getNext() < 1000) {
            List<Long> collect = subgroupDataDTOList.stream().map(SubgroupDataDTO::getF_SGRP).collect(Collectors.toList());
            subgroupDataSelectionDTO.setSgrpList(collect);
        }

        /*获取子组对应测试*/
        List<SGRP_VAL_DTO> sgrpValDtoList = baseMapper.getSgrpValDtoList(subgroupDataSelectionDTO);
        Map<Long, List<SGRP_VAL_DTO>> map = sgrpValDtoList.stream().collect(Collectors.groupingBy(SGRP_VAL_DTO::getF_SGRP));

        /*获取子组对应描述符*/
        List<SGRP_DSC_DTO> sgrpDscDtoList = baseMapper.getSgrpDscDtoList(subgroupDataSelectionDTO);
        Map<Long, List<SGRP_DSC_DTO>> sgrpDscMap = sgrpDscDtoList.stream().collect(Collectors.groupingBy(SGRP_DSC_DTO::getF_SGRP));

        List<SGRP_CMT_DTO> sgrpCmtDtoList =new ArrayList<>();
        /*获取子组备注*/
        final Set<Long> longSet = subgroupDataDTOMap.keySet();
        if (CollectionUtils.isNotEmpty(longSet)) {
            List<Long> longList = new ArrayList<>(longSet);
            sgrpCmtDtoList = sgrpCmtService.getBySgrpList(longList);
        }
        Map<Long, List<SGRP_CMT_DTO>> sgrpCmtMap = CollectionUtils.isEmpty(sgrpCmtDtoList) ? Collections.emptyMap() : sgrpCmtDtoList.stream().collect(Collectors.groupingBy(SGRP_CMT_DTO::getF_SGRP));


//        /*获取子组各个属性名称*/
//        /*获取批次名称*/
        List<LOT_INF_DTO> lotInfDtoList = baseMapper.getLotList(subgroupDataSelectionDTO);
        Map<Long, LOT_INF_DTO> lotInfDtoMap = CollectionToMapUtils.convertMap(lotInfDtoList, LOT_INF_DTO::getF_LOT);
        /*获取工作名称*/
        List<JOB_DAT_DTO> jobDatDtoList = baseMapper.getJobList(subgroupDataSelectionDTO);
        Map<Long, JOB_DAT_DTO> jobDatDtoMap = CollectionToMapUtils.convertMap(jobDatDtoList, JOB_DAT_DTO::getF_JOB);

        /*获取班次名称*/
        List<SHIFT_DAT_DTO> shiftDatDtoList = baseMapper.getShiftList(subgroupDataSelectionDTO);
        Map<Long, SHIFT_DAT_DTO> shiftDatDtoMap = CollectionToMapUtils.convertMap(shiftDatDtoList, SHIFT_DAT_DTO::getF_SHIFT);
//
//        /*获取版本名称*/
//        List<PART_REV_DTO> partRevDtoList = baseMapper.getPtrvtList(subgroupDataSelectionDTO);
//        Map<Long, PART_REV_DTO> partRevDtoMap = CollectionToMapUtils.convertMap(partRevDtoList, PART_REV_DTO::getF_PTRV);

        /*获取序列号名称*/
        List<SN_INF_DTO> snInfDtoList = baseMapper.getSnList(subgroupDataSelectionDTO);
        Map<Long, SN_INF_DTO> snInfDtoMap = CollectionToMapUtils.convertMap(snInfDtoList, SN_INF_DTO::getF_PART);

        List<SubgroupDataDTO> list = new ArrayList<>();
        map.forEach((k, v) -> {
            SubgroupDataDTO subgroupDataDTO = subgroupDataDTOMap.get(k);
            if (subgroupDataDTO != null) {
                if (CollectionUtils.isNotEmpty(v)) {
                    subgroupDataDTO.setSgrpValDtoList(v);
                }
                if(CollectionUtils.isNotEmpty(subgroupDataDTO.getSgrpValDtoList())) {
                    subgroupDataDTO.getSgrpValDtoList().forEach(sgrpValDto -> {
                        sgrpValDto.setSgrpValChildDto(JSONObject.parseObject(sgrpValDto.getF_DATA(), SGRP_VAL_CHILD_DTO.class));
                    });
                    if(ObjectUtils.isNotEmpty(subgroupDataDTO.getSgrpValDtoList().get(0).getF_SGSZ()) && subgroupDataDTO.getSgrpValDtoList().get(0).getF_SGSZ()>0){
                        subgroupDataDTO.setF_SGSZ(subgroupDataDTO.getSgrpValDtoList().get(0).getF_SGSZ());
                    }
                }
                if (CollectionUtils.isNotEmpty(sgrpDscMap.get(subgroupDataDTO.getF_SGRP()))) {
                    subgroupDataDTO.setSgrpDscList(sgrpDscMap.get(subgroupDataDTO.getF_SGRP()));
                }

                if (lotInfDtoMap.get(subgroupDataDTO.getF_LOT()) != null) {
                    subgroupDataDTO.setLotInfDto(lotInfDtoMap.get(subgroupDataDTO.getF_LOT()));
                }
//
                if (jobDatDtoMap.get(subgroupDataDTO.getF_JOB()) != null) {
                    JOB_DAT_DTO jobDatDto = jobDatDtoMap.get(subgroupDataDTO.getF_JOB());
                    subgroupDataDTO.setJobDatDto(jobDatDto);
                }
//
                if (shiftDatDtoMap.get(subgroupDataDTO.getF_SHIFT()) != null) {
                    SHIFT_DAT_DTO shiftDatDto = shiftDatDtoMap.get(subgroupDataDTO.getF_SHIFT());
                    subgroupDataDTO.setShiftDatDto(shiftDatDto);
                }
//
//                if (partRevDtoMap.get(subgroupDataDTO.getF_REV()) != null) {
//                    subgroupDataDTO.setPtrvName(partRevDtoMap.get(subgroupDataDTO.getF_REV()).getF_NAME());
//                }
//
                if (snInfDtoMap.get(subgroupDataDTO.getF_PART()) != null) {
                    subgroupDataDTO.setSnName(snInfDtoMap.get(subgroupDataDTO.getF_PART()).getF_NAME());
                }
                if(CollectionUtils.isNotEmpty(sgrpCmtMap.get(subgroupDataDTO.getF_SGRP()))){
                    subgroupDataDTO.setSgrpCmtList(sgrpCmtMap.get(subgroupDataDTO.getF_SGRP()));
                }

                list.add(subgroupDataDTO);
            }
        });
        return list.stream().sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP).reversed()).collect(Collectors.toList());
    }


    @Override
    public SubgroupDataDTO getTopOne(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        return baseMapper.getTopOne(subgroupDataSelectionDTO);
    }

    @Override
    @Transactional
    public void add(List<SubgroupDataVO> subgroupDataVOList) {
        /*生成抽样唯一标识*/
        UUID uuid = UUID.randomUUID();
        if (CollectionUtils.isEmpty(subgroupDataVOList))
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        /*获取检查计划子计划数量*/
        INSPECTION_PLAN_INF_DTO inspectionPlanInfDto = inspectionPlanInfService.getInfo(subgroupDataVOList.get(0).getF_INSP_PLAN());
        if (inspectionPlanInfDto == null) return;
        /*获取子计划列表*/
        List<Long> childList = new ArrayList<>();
        if (inspectionPlanInfDto.getF_TYPE() == 1) {
            subgroupDataVOList.forEach(subgroupDataVO -> {
                Set<Long> collect = subgroupDataVO.getSgrpValChildDtoList().stream().map(SGRP_VAL_CHILD_DTO::getChildId).collect(Collectors.toSet());
                childList.addAll(collect);
            });

        } else {
            inspectionPlanInfDto.getInspectionPlanChildDtoList().forEach((k, v) -> {
                childList.add(v.get(0).getChildId());
            });
        }

        MANUFACTURING_PROCESS_INF manufacturingProcessInf = manufacturingProcessInfService.getById(subgroupDataVOList.get(0).getF_MFPS());
        /*获取已处理的子计划*/
        if (StringUtils.isNotEmpty(subgroupDataVOList.get(0).getF_SAMPLE_ID())) {
            LambdaQueryWrapper<SGRP_INF> sgrpInfLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sgrpInfLambdaQueryWrapper.eq(SGRP_INF::getF_SAMPLE_ID, subgroupDataVOList.get(0).getF_SAMPLE_ID())
                    .eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
            List<SGRP_INF> sgrpInfList = baseMapper.selectList(sgrpInfLambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(sgrpInfList)) {
                LambdaQueryWrapper<SGRP_VAL> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(SGRP_VAL::getF_SGRP, sgrpInfList.stream().map(SGRP_INF::getF_SGRP).collect(Collectors.toList()));
                List<SGRP_VAL> sgrpValList = sgrpValService.list(queryWrapper);
                Set<Long> collect = sgrpValList.stream().map(SGRP_VAL::getF_CHILD).collect(Collectors.toSet());
                /*将已处理的子计划过滤*/
                childList.removeAll(collect);
            }
        }

        EMPL_INF_DTO emplInfDto = SecurityUtils.getLoginUser() == null ?
                remoteUserService.info(redisService.getCacheObject(RedisConstant.ADMIN_USER_ID)).getData() : SecurityUtils.getLoginUser().getSysUser();
        List<SubgroupDataVO> subgroupDataList = new ArrayList<>();
        subgroupDataVOList.forEach(subgroupDataVO -> {
            if (StringUtils.isEmpty(subgroupDataVO.getF_SAMPLE_ID()))
                subgroupDataVO.setF_SAMPLE_ID(uuid.toString());
            subgroupDataVO.setF_CRUE(emplInfDto.getF_EMPL());
            subgroupDataVO.setF_EDUE(emplInfDto.getF_EMPL());
            subgroupDataVO.setF_PLNT(manufacturingProcessInf.getF_PLNT());
            if (CollectionUtils.isNotEmpty(subgroupDataVO.getPrcsList())) {
                /*多过程*/
                subgroupDataVO.getPrcsList().forEach(prcs -> {
                    SubgroupDataVO dataVO = SubgroupDataConvert.INSTANCE.convert(subgroupDataVO);
                    dataVO.setF_PRCS(prcs);
                    List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList = new ArrayList<>();
                    dataVO.getSgrpValChildDtoList().forEach(sgrpValChildDto -> {
                        SGRP_VAL_CHILD_DTO convert = SubgroupDataConvert.INSTANCE.convert(sgrpValChildDto);
                        List<SGRP_VAL_CHILD_DTO.Test> list = new ArrayList<>();
                        List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValChildDto.getTestList().stream()
                                .filter(s -> s.getF_PRCS().equals(prcs)).collect(Collectors.toList());
                        testList.forEach(testDto -> {
                            SGRP_VAL_CHILD_DTO.Test test = SubgroupDataConvert.INSTANCE.convert(testDto);
                            list.add(test);
                        });
                        convert.setTestList(list);
                        sgrpValChildDtoList.add(convert);
                    });
                    dataVO.setSgrpValChildDtoList(sgrpValChildDtoList);

                    subgroupDataList.add(dataVO);
                });
            } else {
                subgroupDataList.add(subgroupDataVO);
            }

            /*判断产品是否输入的名称*/
            if (StringUtils.isNotEmpty(subgroupDataVO.getPartName())) {
                List<HIERARCHY_INF_DTO> hierarchyInfDtoList = new ArrayList<>();
                HIERARCHY_INF_DTO.getHierarchyInfList(hierarchyInfDtoList, emplInfDto.getHierarchyInfDto(), HIERARCHY_INFTypeEnum.FACTORY.getType());
                if (CollectionUtils.isEmpty(hierarchyInfDtoList))
                    throw new BusinessException(SystemExceptionEnum.USER_HIER_NOT_EXISTS);
                /*先查询*/
                LambdaQueryWrapper<PART_INF> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(PART_INF::getF_NAME, subgroupDataVO.getPartName()).eq(PART_INF::getF_DEL, DelFlagEnum.USE.getType())
                        .in(PART_INF::getF_PLNT, hierarchyInfDtoList.stream().map(HIERARCHY_INF_DTO::getF_HIER).collect(Collectors.toList()));
                List<PART_INF> list = partInfService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    PART_INF_VO partInfVo = new PART_INF_VO();
                    partInfVo.setF_NAME(subgroupDataVO.getPartName());
                    partInfVo.setF_PLNT(hierarchyInfDtoList.stream().map(HIERARCHY_INF_DTO::getF_HIER).collect(Collectors.toList()).get(0));
                    partInfVo.setF_EDUE(emplInfDto.getF_EMPL());
                    partInfVo.setF_CRUE(emplInfDto.getF_EMPL());
                    if (StringUtils.isEmpty(subgroupDataVO.getPtrvName()) && StringUtils.isEmpty(subgroupDataVO.getF_REV())) {
                        partInfService.add(partInfVo);
                        subgroupDataVO.setF_REV(partInfVo.getPartRevId());
                    } else {
                        PART_INF partInf = new PART_INF();
                        BeanUtils.copyPropertiesIgnoreNull(partInfVo, partInf);
                        partInfService.save(partInf);
                        partInfVo.setF_PART(partInf.getF_PART());
                    }
                    subgroupDataVO.setF_PART(partInfVo.getF_PART());
                } else {
                    subgroupDataVO.setF_PART((list.get(0).getF_PART()));
                    if (StringUtils.isEmpty(subgroupDataVO.getPtrvName()) && StringUtils.isEmpty(subgroupDataVO.getF_REV())) {
                        LambdaQueryWrapper<PART_REV> revQueryWrapper = new LambdaQueryWrapper<>();
                        revQueryWrapper.eq(PART_REV::getF_PART, subgroupDataVO.getF_PART())
                                .eq(PART_REV::getF_DEL, DelFlagEnum.USE.getType());
                        List<PART_REV> revList = partRevService.list(revQueryWrapper);
                        if (CollectionUtils.isNotEmpty(revList)) {
                            subgroupDataVO.setF_REV(revList.get(0).getF_PTRV());
                        }
                    }
                }
            }

            /*判断测试是否输入的名称*/

            /*判断产品版本是否输入的名称*/
            if (StringUtils.isNotEmpty(subgroupDataVO.getPtrvName())) {
                /*先查询*/
                LambdaQueryWrapper<PART_REV> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(PART_REV::getF_NAME, subgroupDataVO.getPtrvName())
                        .eq(PART_REV::getF_PART, subgroupDataVO.getF_PART())
                        .eq(PART_REV::getF_DEL, DelFlagEnum.USE.getType());
                List<PART_REV> list = partRevService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    PART_INF_VO partInfVo = new PART_INF_VO();
                    partInfVo.setPartRevName(subgroupDataVO.getPtrvName());
                    partInfVo.setF_PART(subgroupDataVO.getF_PART());
                    partInfVo.setF_EDUE(emplInfDto.getF_EMPL());
                    partInfVo.setF_CRUE(emplInfDto.getF_EMPL());
                    PART_REV partRev = PART_REV_VO.initPartRev(partInfVo);
                    partRevService.save(partRev);
                    subgroupDataVO.setF_REV(partRev.getF_PTRV());
                } else {
                    subgroupDataVO.setF_REV((list.get(0).getF_PTRV()));
                }
            }

            /*判断过程是否输入的名称*/
            if (StringUtils.isNotEmpty(subgroupDataVO.getPrcsName())) {
                /*先查询*/
                LambdaQueryWrapper<PRCS_INF> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(PRCS_INF::getF_NAME, subgroupDataVO.getPrcsName())
                        .eq(PRCS_INF::getF_DEL, DelFlagEnum.USE.getType());
                List<PRCS_INF> list = prcsInfService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    PRCS_INF_VO prcsInfVo = new PRCS_INF_VO();
                    prcsInfVo.setF_NAME(subgroupDataVO.getPrcsName());
                    prcsInfVo.setF_EDUE(emplInfDto.getF_EMPL());
                    prcsInfVo.setF_CRUE(emplInfDto.getF_EMPL());
                    prcsInfService.add(prcsInfVo);
                    subgroupDataVO.setF_PRCS(prcsInfVo.getF_PRCS());
                } else {
                    subgroupDataVO.setF_PRCS(list.get(0).getF_PRCS());
                }
            }

            /*判断批次是否输入的名称*/
            if (StringUtils.isNotEmpty(subgroupDataVO.getLotName())) {
                /*先查询*/
                LambdaQueryWrapper<LOT_INF> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(LOT_INF::getF_NAME, subgroupDataVO.getLotName())
                        .eq(LOT_INF::getF_DEL, DelFlagEnum.USE.getType());
                List<LOT_INF> list = lotInfService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    LOT_INF_VO lotInfVo = new LOT_INF_VO();
                    lotInfVo.setF_NAME(subgroupDataVO.getLotName());
                    lotInfVo.setF_EDUE(emplInfDto.getF_EMPL());
                    lotInfVo.setF_CRUE(emplInfDto.getF_EMPL());
                    lotInfService.add(lotInfVo);
                    subgroupDataVO.setF_LOT(lotInfVo.getF_LOT());
                } else {
                    subgroupDataVO.setF_LOT(list.get(0).getF_LOT());
                }
            }

            /*判断工作组是否输入的名称*/
            if (StringUtils.isNotEmpty(subgroupDataVO.getJobGrpName())) {
                /*先查询*/
                LambdaQueryWrapper<JOB_GRP> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(JOB_GRP::getF_NAME, subgroupDataVO.getJobGrpName())
                        .eq(JOB_GRP::getF_DEL, DelFlagEnum.USE.getType());
                List<JOB_GRP> list = jobGrpService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    JOB_GRP_VO jobGrpVo = new JOB_GRP_VO();
                    jobGrpVo.setF_NAME(subgroupDataVO.getJobGrpName());
                    jobGrpVo.setF_EDUE(emplInfDto.getF_EMPL());
                    jobGrpVo.setF_CRUE(emplInfDto.getF_EMPL());
                    jobGrpService.add(jobGrpVo);
                    subgroupDataVO.setF_JBGP(jobGrpVo.getF_JBGP());
                } else {
                    subgroupDataVO.setF_JBGP(list.get(0).getF_JBGP());
                }
            }

            /*判断工作是否输入的名称*/
            if (StringUtils.isNotEmpty(subgroupDataVO.getJobName())) {
                if (StringUtils.isEmpty(subgroupDataVO.getF_JBGP()))
                    throw new BusinessException(DataManagementExceptionEnum.JOB_GRP_NOT_EXISTS);
                /*先查询*/
                LambdaQueryWrapper<JOB_DAT> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(JOB_DAT::getF_NAME, subgroupDataVO.getJobName())
                        .eq(JOB_DAT::getF_JBGP, subgroupDataVO.getF_JBGP())
                        .eq(JOB_DAT::getF_DEL, DelFlagEnum.USE.getType());
                List<JOB_DAT> list = jobDatService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    JOB_DAT_VO jobDatVo = new JOB_DAT_VO();
                    jobDatVo.setF_NAME(subgroupDataVO.getJobName());
                    jobDatVo.setF_JBGP(subgroupDataVO.getF_JBGP());
                    jobDatVo.setF_EDUE(emplInfDto.getF_EMPL());
                    jobDatVo.setF_CRUE(emplInfDto.getF_EMPL());
                    jobDatService.add(jobDatVo);
                    subgroupDataVO.setF_JOB(jobDatVo.getF_JOB());
                } else {
                    subgroupDataVO.setF_JBGP(list.get(0).getF_JBGP());
                }
            }

            /*判断班次组是否输入的名称*/
            if (StringUtils.isNotEmpty(subgroupDataVO.getShiftGrpName())) {
                /*先查询*/
                LambdaQueryWrapper<SHIFT_GRP> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SHIFT_GRP::getF_NAME, subgroupDataVO.getShiftGrpName())
                        .eq(SHIFT_GRP::getF_DEL, DelFlagEnum.USE.getType());
                List<SHIFT_GRP> list = shiftGrpService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    SHIFT_GRP_VO shiftGrpVo = new SHIFT_GRP_VO();
                    shiftGrpVo.setF_NAME(subgroupDataVO.getShiftGrpName());
                    shiftGrpVo.setF_EDUE(emplInfDto.getF_EMPL());
                    shiftGrpVo.setF_CRUE(emplInfDto.getF_EMPL());
                    shiftGrpService.add(shiftGrpVo);
                    subgroupDataVO.setF_SHGP(shiftGrpVo.getF_SHGP());
                } else {
                    subgroupDataVO.setF_SHGP(list.get(0).getF_SHGP());
                }
            }

            /*判断班次是否输入的名称*/
            if (StringUtils.isNotEmpty(subgroupDataVO.getShiftName())) {
                if (StringUtils.isEmpty(subgroupDataVO.getF_SHGP()))
                    throw new BusinessException(DataManagementExceptionEnum.SHIFT_GRP_NOT_EXISTS);
                /*先查询*/
                LambdaQueryWrapper<SHIFT_DAT> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SHIFT_DAT::getF_NAME, subgroupDataVO.getShiftName())
                        .eq(SHIFT_DAT::getF_SHGP, subgroupDataVO.getF_SHGP())
                        .eq(SHIFT_DAT::getF_DEL, DelFlagEnum.USE.getType());
                List<SHIFT_DAT> list = shiftDatService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    SHIFT_DAT_VO shiftDatVo = new SHIFT_DAT_VO();
                    shiftDatVo.setF_NAME(subgroupDataVO.getShiftName());
                    shiftDatVo.setF_SHGP(subgroupDataVO.getF_SHGP());
                    shiftDatVo.setF_EDUE(emplInfDto.getF_EMPL());
                    shiftDatVo.setF_CRUE(emplInfDto.getF_EMPL());
                    shiftDatService.add(shiftDatVo);
                    subgroupDataVO.setF_SHIFT(shiftDatVo.getF_SHIFT());
                } else {
                    subgroupDataVO.setF_SHIFT(list.get(0).getF_SHIFT());
                }
            }

            subgroupDataVO.getSgrpDscList().forEach(sgrpDsc -> {

                /*判断描述符组是否输入的名称*/
                if (StringUtils.isNotEmpty(sgrpDsc.getDescGrpName())) {
                    /*先查询*/
                    LambdaQueryWrapper<DESC_GRP> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(DESC_GRP::getF_NAME, sgrpDsc.getDescGrpName())
                            .eq(DESC_GRP::getF_DEL, DelFlagEnum.USE.getType());
                    List<DESC_GRP> list = descGrpService.list(queryWrapper);
                    if (CollectionUtils.isEmpty(list)) {
                        DESC_GRP_VO descGrpVo = new DESC_GRP_VO();
                        descGrpVo.setF_NAME(sgrpDsc.getDescGrpName());
                        descGrpVo.setF_EDUE(emplInfDto.getF_EMPL());
                        descGrpVo.setF_CRUE(emplInfDto.getF_EMPL());
                        descGrpService.add(descGrpVo);
                        sgrpDsc.setF_DSGP(descGrpVo.getF_DSGP());
                    } else {
                        sgrpDsc.setF_DSGP(list.get(0).getF_DSGP());
                    }
                }

                /*判断描述符是否输入的名称*/
                if (StringUtils.isNotEmpty(sgrpDsc.getDescName())) {
                    if (StringUtils.isEmpty(sgrpDsc.getF_DSGP()))
                        throw new BusinessException(DataManagementExceptionEnum.SHIFT_GRP_NOT_EXISTS);
                    /*先查询*/
                    LambdaQueryWrapper<DESC_DAT> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(DESC_DAT::getF_NAME, sgrpDsc.getDescName())
                            .eq(DESC_DAT::getF_DSGP, sgrpDsc.getF_DSGP())
                            .eq(DESC_DAT::getF_DEL, DelFlagEnum.USE.getType());
                    List<DESC_DAT> list = descDatService.list(queryWrapper);
                    if (CollectionUtils.isEmpty(list)) {
                        DESC_DAT_VO descDatVo = new DESC_DAT_VO();
                        descDatVo.setF_NAME(sgrpDsc.getDescName());
                        descDatVo.setF_DSGP(sgrpDsc.getF_DSGP());
                        descDatVo.setF_EDUE(emplInfDto.getF_EMPL());
                        descDatVo.setF_CRUE(emplInfDto.getF_EMPL());
                        descDatService.add(descDatVo);
                        sgrpDsc.setF_DESC(descDatVo.getF_DESC());
                    } else {
                        sgrpDsc.setF_DESC(list.get(0).getF_DESC());
                    }
                }
            });


            /*判断是缺陷代码是否输入的名称*/
            subgroupDataVO.getSgrpValChildDtoList().forEach(sgrpValChildDto -> {
                sgrpValChildDto.getTestList().forEach(test -> {
                    /*判断缺陷代码组是否输入的名称*/
                    if (StringUtils.isNotEmpty(test.getDefectGrpName())) {
                        /*先查询*/
                        LambdaQueryWrapper<DEF_GRP> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(DEF_GRP::getF_NAME, test.getDefectGrpName())
                                .eq(DEF_GRP::getF_DEL, DelFlagEnum.USE.getType());
                        List<DEF_GRP> list = defGrpService.list(queryWrapper);
                        if (CollectionUtils.isEmpty(list)) {
                            DEF_GRP_VO defGrpVo = new DEF_GRP_VO();
                            defGrpVo.setF_NAME(test.getDefectGrpName());
                            defGrpVo.setF_EDUE(emplInfDto.getF_EMPL());
                            defGrpVo.setF_CRUE(emplInfDto.getF_EMPL());
                            defGrpService.add(defGrpVo);
                            test.setDefectGrpId(defGrpVo.getF_DFGP());
                        } else {
                            test.setDefectGrpId(list.get(0).getF_DFGP());
                        }
                    }

                    if (StringUtils.isNotEmpty(test.getDefectName())) {
                        if (StringUtils.isEmpty(test.getDefectGrpId()))
                            throw new BusinessException(DataManagementExceptionEnum.DES_GRP_NOT_EXISTS);
                        /*先查询*/
                        LambdaQueryWrapper<DEF_DAT> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(DEF_DAT::getF_NAME, test.getDefectName())
                                .eq(DEF_DAT::getF_DFGP, test.getDefectGrpId())
                                .eq(DEF_DAT::getF_DEL, DelFlagEnum.USE.getType());
                        List<DEF_DAT> list = defDatService.list(queryWrapper);
                        if (CollectionUtils.isEmpty(list)) {
                            DEF_DAT_VO defDatVo = new DEF_DAT_VO();
                            defDatVo.setF_DFGP(test.getDefectGrpId());
                            defDatVo.setF_NAME(test.getDefectName());
                            defDatVo.setF_EDUE(emplInfDto.getF_EMPL());
                            defDatVo.setF_CRUE(emplInfDto.getF_EMPL());
                            defDatService.add(defDatVo);
                            test.setDefectId(defDatVo.getF_DEF());
                        } else {
                            test.setDefectId(list.get(0).getF_DEF());
                        }
                    }
                });
            });

            /*判断测试是否输入的名称*/
            subgroupDataVO.getSgrpValChildDtoList().forEach(sgrpValChildDto -> {
                if (CollectionUtils.isEmpty(sgrpValChildDto.getTestList())) {
                    throw new BusinessException(DataManagementExceptionEnum.TEST_VAL_IS_NULL);
                }
                /*排除添加的子计划*/
                childList.remove(sgrpValChildDto.getChildId());
                /*判断测试是否输入的名称*/
                if (StringUtils.isNotEmpty(sgrpValChildDto.getTestName()) && sgrpValChildDto.getTestId() == null) {
                    /*先查询*/
                    LambdaQueryWrapper<TEST_INF> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(TEST_INF::getF_NAME, sgrpValChildDto.getTestName())
                            .eq(TEST_INF::getF_DEL, DelFlagEnum.USE.getType());
                    List<TEST_INF> list = testInfService.list(queryWrapper);
                    if (CollectionUtils.isEmpty(list)) {
                        TEST_INF_VO testInfVo = new TEST_INF_VO();
                        testInfVo.setF_NAME(sgrpValChildDto.getTestName());
                        testInfVo.setF_EDUE(emplInfDto.getF_EMPL());
                        testInfVo.setF_CRUE(emplInfDto.getF_EMPL());
                        testInfService.add(testInfVo);
                        sgrpValChildDto.setTestId(testInfVo.getF_TEST());
                    } else {
                        sgrpValChildDto.setTestId(list.get(0).getF_TEST());
                    }
                }
            });
            if (CollectionUtils.isNotEmpty(childList)) {
                subgroupDataVO.setF_FINISH_STATUS(YesOrNoEnum.NO.getType());
            }
        });
        /*保存未完成的子计划*/
        if (CollectionUtils.isNotEmpty(childList)) {
            childList.forEach(child -> {
                subgroupDataList.forEach(subgroupDataVO -> {
                    SGRP_INF_UNFINISHED sgrpInfUnfinished = new SGRP_INF_UNFINISHED();
                    BeanUtils.copyPropertiesIgnoreNull(subgroupDataVO, sgrpInfUnfinished);
                    sgrpInfUnfinished.setF_CHILD(child);
                    sgrpInfUnfinishedMapper.insert(sgrpInfUnfinished);
                });
            });
        }

        remoteMqService.send(subgroupDataList);
    }

    @Override
    public List<SubgroupDataDTO> getViewDataSubgroupDataDTOList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        getDescList(subgroupDataSelectionDTO);
        List<SubgroupDataDTO> subgroupDataDTOList = baseMapper.getViewDataSubgroupDataDTOList(subgroupDataSelectionDTO);
        subgroupDataDTOList = structure(subgroupDataDTOList, subgroupDataSelectionDTO, true);
        cleanTemp(subgroupDataSelectionDTO);
        return subgroupDataDTOList;
    }

    /**
     * 描述符条件查询
     *
     * @param subgroupDataSelectionDTO
     */
    private void getDescList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        if (CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getDescList())) {
            List<SGRP_DSC> sgrpDscList = sgrpDscService.findByDescIds(subgroupDataSelectionDTO.getDescList());
            if (CollectionUtils.isNotEmpty(sgrpDscList)) {
                List<Long> list = sgrpDscList.stream().map(SGRP_DSC::getF_SGRP).distinct().collect(Collectors.toList());
                if (list.size() > 1000) {
                    String uuid = UUID.randomUUID().toString();
                    tempTableInfService.batchAdd(list, uuid);
                    subgroupDataSelectionDTO.setIsTemp(YesOrNoEnum.YES.getType());
                    subgroupDataSelectionDTO.setTempIdentify(uuid);
                }
                subgroupDataSelectionDTO.setSgrpList(list);
            }
        }
    }

    @Override
    public long getViewDataTotal(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        return baseMapper.getViewDataTotal(subgroupDataSelectionDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum) {
        LambdaQueryWrapper<SGRP_INF> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<SGRP_INF> list = new ArrayList<>();
        SGRP_INF sgrpInf = SGRP_INF.init();
        SGRP_INF_A sgrpInfA = SGRP_INF_A.init();
        switch (paretoAnalyseTypeEnum) {
            case PART_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_PART, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                sgrpInf.setF_DEL(YesOrNoEnum.YES.getType());
                sgrpInfA.setF_DEL(YesOrNoEnum.YES.getType());
                break;
            case PRCS_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_PRCS, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                sgrpInf.setF_DEL(YesOrNoEnum.YES.getType());
                sgrpInfA.setF_DEL(YesOrNoEnum.YES.getType());
                break;
            case TEST_DAT:
                LambdaQueryWrapper<SGRP_VAL> testQuery = new LambdaQueryWrapper<>();
                testQuery.in(SGRP_VAL::getF_TEST, ids);
                List<SGRP_VAL> valList = sgrpValService.list(testQuery);
                if (CollectionUtils.isNotEmpty(valList)) {
                    List<Long> sgrpIds = valList.stream().map(SGRP_VAL::getF_SGRP).collect(Collectors.toList());
                    lambdaQueryWrapper.in(SGRP_INF::getF_SGRP, sgrpIds);
                    list = baseMapper.selectList(lambdaQueryWrapper);
                    sgrpInf.setF_DEL(YesOrNoEnum.YES.getType());
                    sgrpInfA.setF_DEL(YesOrNoEnum.YES.getType());
                }
                break;
            case LOT_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_LOT, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                sgrpInf.setF_LOT(0L);
                sgrpInfA.setF_LOT(0L);
                break;
            case SHIFT_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_SHIFT, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                sgrpInf.setF_SHIFT(0L);
                sgrpInfA.setF_SHIFT(0L);
                break;
            case SHIFT_GRP:
                LambdaQueryWrapper<SHIFT_DAT> shiftDatWrapper = new LambdaQueryWrapper<>();
                shiftDatWrapper.in(SHIFT_DAT::getF_SHGP, ids).eq(SHIFT_DAT::getF_DEL, DelFlagEnum.USE.getType());
                List<SHIFT_DAT> shiftDatList = shiftDatService.list(shiftDatWrapper);
                if (CollectionUtils.isNotEmpty(shiftDatList)) {
                    List<Long> shiftIds = shiftDatList.stream().map(SHIFT_DAT::getF_SHIFT).collect(Collectors.toList());
                    lambdaQueryWrapper.in(SGRP_INF::getF_SHIFT, shiftIds).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                    list = baseMapper.selectList(lambdaQueryWrapper);
                    sgrpInf.setF_SHIFT(0L);
                    sgrpInfA.setF_SHIFT(0L);
                }
                break;
            case JOB_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_JOB, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                sgrpInf.setF_JOB(0L);
                sgrpInfA.setF_JOB(0L);
                break;
            case JOB_GRP:
                LambdaQueryWrapper<JOB_DAT> jobDatWrapper = new LambdaQueryWrapper<>();
                jobDatWrapper.in(JOB_DAT::getF_JBGP, ids).eq(JOB_DAT::getF_DEL, DelFlagEnum.USE.getType());
                List<JOB_DAT> jobDatList = jobDatService.list(jobDatWrapper);
                if (CollectionUtils.isNotEmpty(jobDatList)) {
                    List<Long> jobIds = jobDatList.stream().map(JOB_DAT::getF_JOB).collect(Collectors.toList());
                    lambdaQueryWrapper.in(SGRP_INF::getF_JOB, jobIds).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                    list = baseMapper.selectList(lambdaQueryWrapper);
                    sgrpInf.setF_SHIFT(0L);
                    sgrpInfA.setF_SHIFT(0L);
                }
                break;
            case DESC_DAT:
                LambdaQueryWrapper<SGRP_DSC> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(SGRP_DSC::getF_DESC, ids);
                sgrpDscService.remove(queryWrapper);
                LambdaQueryWrapper<SGRP_DSC_A> queryWrapperA = new LambdaQueryWrapper<>();
                queryWrapperA.in(SGRP_DSC_A::getF_DESC, ids);
                sgrpDscAService.remove(queryWrapperA);
                break;
            case DESC_GRP:
                LambdaQueryWrapper<SGRP_DSC> grpWrapper = new LambdaQueryWrapper<>();
                grpWrapper.in(SGRP_DSC::getF_DSGP, ids);
                sgrpDscService.remove(grpWrapper);
                LambdaQueryWrapper<SGRP_DSC_A> grpWrapperA = new LambdaQueryWrapper<>();
                grpWrapperA.in(SGRP_DSC_A::getF_DSGP, ids);
                sgrpDscAService.remove(grpWrapperA);
                break;
            case PTRV_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_REV, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                sgrpInf.setF_REV(0L);
                sgrpInfA.setF_REV(0L);
                break;
        }

        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> collect = list.stream().map(SGRP_INF::getF_SGRP).collect(Collectors.toList());

            LambdaQueryWrapper<SGRP_INF> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(SGRP_INF::getF_SGRP, collect);
            baseMapper.update(sgrpInf, wrapper);

            LambdaQueryWrapper<SGRP_INF_A> wrapperA = new LambdaQueryWrapper<>();
            wrapperA.in(SGRP_INF_A::getF_SGRP, collect);
            sgrpInfAService.update(sgrpInfA, wrapperA);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(SubgroupDataVO subgroupDataVO) {
        SGRP_INF sgrpInf = new SGRP_INF();
        BeanUtils.copyPropertiesIgnoreNull(subgroupDataVO, sgrpInf);
        /*修改子组信息 子组缓存信息*/
        this.updateById(sgrpInf);
        SGRP_INF_A sgrpInfA = new SGRP_INF_A();
        BeanUtils.copyPropertiesIgnoreNull(subgroupDataVO, sgrpInfA);
        sgrpInfAService.updateById(sgrpInfA);
        subgroupDataVO.setF_EDUE(SecurityUtils.getUserId());
        /*修改子组测试信息 缓存信息*/
        if (CollectionUtils.isNotEmpty(subgroupDataVO.getSgrpValChildDtoList())) {
            LambdaQueryWrapper<SGRP_VAL> valWrapper = new LambdaQueryWrapper<>();
            valWrapper.eq(SGRP_VAL::getF_SGRP, subgroupDataVO.getF_SGRP());
            sgrpValService.remove(valWrapper);
            LambdaQueryWrapper<SGRP_VAL_A> valAWrapper = new LambdaQueryWrapper<>();
            valAWrapper.eq(SGRP_VAL_A::getF_SGRP, subgroupDataVO.getF_SGRP());
            sgrpValAService.remove(valAWrapper);
            List<SGRP_VAL> sgrpValList = SGRP_VAL_CHILD_DTO.computeVal(subgroupDataVO, subgroupDataVO.getSgrpValChildDtoList());
            sgrpValService.saveBatch(sgrpValList);

            List<SGRP_VAL_A> sgrpValAList = SubgroupDataConvert.INSTANCE.convertValAList(sgrpValList);
            sgrpValAService.saveBatch(sgrpValAList);
        }

        /*修改子组描述符信息 缓存信息*/
        if (CollectionUtils.isNotEmpty(subgroupDataVO.getSgrpDscList())) {
            LambdaQueryWrapper<SGRP_DSC> dscWrapper = new LambdaQueryWrapper<>();
            dscWrapper.eq(SGRP_DSC::getF_SGRP, subgroupDataVO.getF_SGRP());
            sgrpDscService.remove(dscWrapper);
            sgrpDscService.saveBatch(subgroupDataVO.getSgrpDscList());

            LambdaQueryWrapper<SGRP_DSC_A> dscAWrapper = new LambdaQueryWrapper<>();
            dscAWrapper.eq(SGRP_DSC_A::getF_SGRP, subgroupDataVO.getF_SGRP());
            sgrpDscAService.remove(dscAWrapper);
            List<SGRP_DSC_A> sgrpDscAList = JSONArray.parseArray(JSONObject.toJSONString(subgroupDataVO.getSgrpDscList()), SGRP_DSC_A.class);
            sgrpDscAService.saveBatch(sgrpDscAList);
        }

        /*修改子组备注信息 缓存信息*/
        if (CollectionUtils.isNotEmpty(subgroupDataVO.getSgrpCmtList())) {
            LambdaQueryWrapper<SGRP_CMT> cmtWrapper = new LambdaQueryWrapper<>();
            cmtWrapper.eq(SGRP_CMT::getF_SGRP, subgroupDataVO.getF_SGRP());
            sgrpCmtService.remove(cmtWrapper);
            sgrpCmtService.saveBatch(subgroupDataVO.getSgrpCmtList());

            LambdaQueryWrapper<SGRP_CMT_A> cmtAWrapper = new LambdaQueryWrapper<>();
            cmtAWrapper.eq(SGRP_CMT_A::getF_SGRP, subgroupDataVO.getF_SGRP());
            sgrpCmtAService.remove(cmtAWrapper);
            List<SGRP_CMT_A> sgrpCmtAList = JSONArray.parseArray(JSONObject.toJSONString(subgroupDataVO.getSgrpCmtList()), SGRP_CMT_A.class);
            sgrpCmtAService.saveBatch(sgrpCmtAList);

        }
    }

    @Override
    public void tempSave(String planId, List<SubgroupDataVO> subgroupDataVOList) {
        Map<String, List<SubgroupDataVO>> cacheMap = redisService.getCacheMap(String.format(RedisConstant.SUBGROUP_INPUT_TEMP, planId, SecurityUtils.getUserId()));
        String uuid = UUID.randomUUID().toString();
        subgroupDataVOList.forEach(subgroupDataVO -> {
            subgroupDataVO.setF_CRTM(DateUtils.getNowDate());
            if (subgroupDataVO.getF_SGTM() == null) {
                subgroupDataVO.setF_SGTM(DateUtils.getNowDate());
            }
            if (StringUtils.isEmpty(subgroupDataVO.getF_SAMPLE_ID())) {
                subgroupDataVO.setF_SAMPLE_ID(uuid);
            }
        });
        cacheMap.put(uuid, subgroupDataVOList);
        redisService.setCacheMap(String.format(RedisConstant.SUBGROUP_INPUT_TEMP, planId, SecurityUtils.getUserId()),
                cacheMap);
    }

    @Override
    public List<SubgroupDataVO> getTempSaveInfo(Long planId) {
        Map<String, List<SubgroupDataVO>> cacheMap = redisService.getCacheMap(String.format(RedisConstant.SUBGROUP_INPUT_TEMP, planId, SecurityUtils.getUserId()));
        List<Object> cacheList = redisService.getCacheList(String.format(RedisConstant.SUBGROUP_PROCESSING_TEMP, planId));
        List<SubgroupDataVO> list = new ArrayList<>();
        cacheMap.forEach((k, v) -> {
            if (cacheList.contains(k)) {
                v.forEach(subgroupDataVO -> {
                    subgroupDataVO.setF_CACHE_STATUS(YesOrNoEnum.YES.getType());
                });
            }
            list.addAll(v);
        });
        return list;
    }

    @Override
    public void delTempSaveInfo(Long planId, SubgroupDataVO subgroupDataVO) {
        String key = String.format(RedisConstant.SUBGROUP_INPUT_TEMP, planId, SecurityUtils.getUserId());
        Map<String, List<SubgroupDataVO>> cacheMap = redisService.getCacheMap(key);
        redisService.deleteObject(key);
        Map<String, List<SubgroupDataVO>> map = new HashMap<>();
        cacheMap.forEach((k, v) -> {
            List<SubgroupDataVO> list = new ArrayList<>();
            v.forEach(subgroupData -> {
                if (!subgroupData.getF_SAMPLE_ID().equals(subgroupDataVO.getF_SAMPLE_ID())) {
                    list.add(subgroupData);
                }
            });
            map.put(k, list);
        });
        redisService.setCacheMap(key, map);
    }

    @Override
    public void delUnfinished(SGRP_INF_UNFINISHED_VO vo) {
        if (StringUtils.isEmpty(vo.getSampleId()) || StringUtils.isEmpty(vo.getChildId())|| StringUtils.isEmpty(vo.getPlanId())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        LambdaQueryWrapper<SGRP_INF_UNFINISHED> delWrapper = new LambdaQueryWrapper<>();
        delWrapper.eq(SGRP_INF_UNFINISHED::getF_SAMPLE_ID, vo.getSampleId());
        delWrapper.eq(SGRP_INF_UNFINISHED::getF_CHILD, vo.getChildId());
        delWrapper.eq(SGRP_INF_UNFINISHED::getF_INSP_PLAN, vo.getPlanId());
        sgrpInfUnfinishedMapper.delete(delWrapper);

        /*待完成子计划*/
        LambdaQueryWrapper<SGRP_INF_UNFINISHED> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(SGRP_INF_UNFINISHED::getF_SAMPLE_ID, vo.getSampleId());
        queryWrapper1.eq(SGRP_INF_UNFINISHED::getF_INSP_PLAN, vo.getPlanId());

        List<SGRP_INF_UNFINISHED> sgrpInfUnfinisheds = sgrpInfUnfinishedMapper.selectList(queryWrapper1);
        if (CollectionUtils.isEmpty(sgrpInfUnfinisheds)) {
            /*查询唯一标识的子组*/
            LambdaQueryWrapper<SGRP_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SGRP_INF::getF_SAMPLE_ID, vo.getSampleId())
                    .eq(SGRP_INF::getF_INSP_PLAN, vo.getPlanId())
                    .eq(SGRP_INF::getF_FINISH_STATUS, YesOrNoEnum.NO.getType())
                    .eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
            List<SGRP_INF> list = this.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                LambdaUpdateWrapper<SGRP_INF> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(SGRP_INF::getF_SAMPLE_ID, vo.getSampleId())
                        .eq(SGRP_INF::getF_INSP_PLAN, vo.getPlanId())
                        .eq(SGRP_INF::getF_FINISH_STATUS, YesOrNoEnum.NO.getType())
                        .eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType())
                        .set(SGRP_INF::getF_FINISH_STATUS, YesOrNoEnum.YES.getType());
                this.update(null, updateWrapper);
            }
        }
    }

    @Override
    public List<SGRP_INF> getCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum) {
        LambdaQueryWrapper<SGRP_INF> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<SGRP_INF> list = new ArrayList<>();
        switch (paretoAnalyseTypeEnum) {
            case PART_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_PART, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
            case PRCS_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_PRCS, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
            case TEST_DAT:
                LambdaQueryWrapper<SGRP_VAL> testQuery = new LambdaQueryWrapper<>();
                testQuery.in(SGRP_VAL::getF_TEST, ids);
                List<SGRP_VAL> valList = sgrpValService.list(testQuery);
                if (CollectionUtils.isNotEmpty(valList)) {
                    List<Long> sgrpIds = valList.stream().map(SGRP_VAL::getF_SGRP).collect(Collectors.toList());
                    lambdaQueryWrapper.in(SGRP_INF::getF_SGRP, sgrpIds);
                    list = baseMapper.selectList(lambdaQueryWrapper);
                }
                break;
            case LOT_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_LOT, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
            case SHIFT_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_SHIFT, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
            case SHIFT_GRP:
                LambdaQueryWrapper<SHIFT_DAT> shiftDatWrapper = new LambdaQueryWrapper<>();
                shiftDatWrapper.in(SHIFT_DAT::getF_SHGP, ids).eq(SHIFT_DAT::getF_DEL, DelFlagEnum.USE.getType());
                List<SHIFT_DAT> shiftDatList = shiftDatService.list(shiftDatWrapper);
                if (CollectionUtils.isNotEmpty(shiftDatList)) {
                    List<Long> shiftIds = shiftDatList.stream().map(SHIFT_DAT::getF_SHIFT).collect(Collectors.toList());
                    lambdaQueryWrapper.in(SGRP_INF::getF_SHIFT, shiftIds).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                    list = baseMapper.selectList(lambdaQueryWrapper);
                }
                break;
            case JOB_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_JOB, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
            case JOB_GRP:
                LambdaQueryWrapper<JOB_DAT> jobDatWrapper = new LambdaQueryWrapper<>();
                jobDatWrapper.in(JOB_DAT::getF_JBGP).eq(JOB_DAT::getF_DEL, DelFlagEnum.USE.getType());
                List<JOB_DAT> jobDatList = jobDatService.list(jobDatWrapper);
                if (CollectionUtils.isNotEmpty(jobDatList)) {
                    List<Long> jobIds = jobDatList.stream().map(JOB_DAT::getF_JOB).collect(Collectors.toList());
                    lambdaQueryWrapper.in(SGRP_INF::getF_JOB, jobIds).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                    list = baseMapper.selectList(lambdaQueryWrapper);
                }
                break;
            case PTRV_DAT:
                lambdaQueryWrapper.in(SGRP_INF::getF_REV, ids).eq(SGRP_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
        }

        return list;
    }

    @Override
    public Integer getWaitDealInfo(Long planId) {
        SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
        subgroupDataSelectionDTO.setF_FINISH_STATUS(YesOrNoEnum.NO.getType())
                .setF_INSP_PLAN(planId);

        /*待完成子计划*/
        LambdaQueryWrapper<SGRP_INF_UNFINISHED> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SGRP_INF_UNFINISHED::getF_INSP_PLAN, planId);
        List<SGRP_INF_UNFINISHED> sgrpInfUnfinisheds = sgrpInfUnfinishedMapper.selectList(queryWrapper);
        return sgrpInfUnfinisheds.stream().map(SGRP_INF_UNFINISHED::getF_SAMPLE_ID).collect(Collectors.toSet()).size();
    }

    @Override
    public List<SubgroupDataDTO> getSgrpInfList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<SubgroupDataDTO> subgroupDataDTOList = baseMapper.getSgrpInfList(subgroupDataSelectionDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) return subgroupDataDTOList;
        subgroupDataDTOList = structure(subgroupDataDTOList, subgroupDataSelectionDTO, true);
        return subgroupDataDTOList;
    }

    @Override
    public long getSgrpInfTotal(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        return baseMapper.getSgrpInfTotal(subgroupDataSelectionDTO);
    }

    /**
     * 子组失效
     *
     * @param sgrpIds
     */
    @Override
    @Transactional
    public void disable(List<Long> sgrpIds) {
        /*获取生效子组*/
        LambdaQueryWrapper<SGRP_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SGRP_INF::getF_SGRP, sgrpIds).eq(SGRP_INF::getF_FLAG, DelFlagEnum.USE.getType());
        List<SGRP_INF> sgrpInfList = baseMapper.selectList(queryWrapper);

        /*获取失效子组*/
        LambdaQueryWrapper<SGRP_INF> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SGRP_INF::getF_SGRP, sgrpIds).eq(SGRP_INF::getF_FLAG, DelFlagEnum.DELETE.getType());
        List<SGRP_INF> infList = baseMapper.selectList(wrapper);

        /*激活子组失效*/
        sgrpInfList.forEach(sgrpInf -> {
            sgrpInf.setF_FLAG(YesOrNoEnum.YES.getType());
        });
        this.updateBatchById(sgrpInfList);

        /*失效子组激活*/
        infList.forEach(sgrpInf -> {
            sgrpInf.setF_FLAG(YesOrNoEnum.NO.getType());
        });
        this.updateBatchById(infList);
    }

    @Override
    @Transactional
    public List<SubgroupDataDTO> findById(Long id) {
        SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
        subgroupDataSelectionDTO.setF_SGRP(id);
        subgroupDataSelectionDTO.setF_FLAG(2);
        List<SubgroupDataDTO> subgroupDataDTOList = getSubgroupDataDTOList(subgroupDataSelectionDTO);
        dataReportService.structureSubgroupDataDTOList(subgroupDataDTOList);
        return subgroupDataDTOList;
    }

    @Override
    public Set<String> getSubgroupIdList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        if (subgroupDataSelectionDTO.getTotalNum() == null) {
            subgroupDataSelectionDTO.setTotalNum(1000);
        }
        getDescList(subgroupDataSelectionDTO);
        List<String> idList = baseMapper.getSubgroupIdList(subgroupDataSelectionDTO);
        cleanTemp(subgroupDataSelectionDTO);
        return new HashSet<>(idList);
    }

    @Override
    public boolean processingTemp(Long planId, SubgroupDataVO subgroupDataVO) {
        String key = String.format(RedisConstant.SUBGROUP_PROCESSING_TEMP, planId);
        List<String> cacheList = redisService.getCacheList(key);
        if (cacheList.contains(subgroupDataVO.getF_SAMPLE_ID())) {
            return false;
        } else {
            redisService.setCacheList(key,
                    Collections.singletonList(subgroupDataVO.getF_SAMPLE_ID()),
                    Constants.REDIS_EXPIRE_TIME);
            return true;
        }
    }

    @Override
    public void cancelProcessingTemp(Long planId, SubgroupDataVO subgroupDataVO) {
        String key = String.format(RedisConstant.SUBGROUP_PROCESSING_TEMP, planId);
        List<String> cacheList = redisService.getCacheList(key);
        if (cacheList.contains(subgroupDataVO.getF_SAMPLE_ID())) {
            cacheList.remove(subgroupDataVO.getF_SAMPLE_ID());
            redisService.deleteObject(key);
            if (CollectionUtils.isNotEmpty(cacheList)) {
                redisService.setCacheList(key, cacheList, Constants.REDIS_EXPIRE_TIME);
            }
        }
    }

    @Override
    public boolean processingPending(Long planId, String childId) {
        String key = String.format(RedisConstant.SUBGROUP_PROCESSING_PENDING, planId);
        List<String> cacheList = redisService.getCacheList(key);
        if (cacheList.contains(childId)) {
            return false;
        } else {
            redisService.setCacheList(key,
                    Collections.singletonList(childId),
                    Constants.REDIS_EXPIRE_TIME);
            return true;
        }
    }

    @Override
    public void cancelProcessingPending(Long planId, String childId) {
        String key = String.format(RedisConstant.SUBGROUP_PROCESSING_PENDING, planId);
        List<String> cacheList = redisService.getCacheList(key);
        if (cacheList.contains(childId)) {
            cacheList.remove(childId);
            redisService.deleteObject(key);
            if (CollectionUtils.isNotEmpty(cacheList)) {
                redisService.setCacheList(key, cacheList, Constants.REDIS_EXPIRE_TIME);
            }
        }
    }

    @Override
    public List<String> getProcessingPending(Long planId) {
        String key = String.format(RedisConstant.SUBGROUP_PROCESSING_PENDING, planId);
        return redisService.getCacheList(key);
    }


    @Override
    public List<SubgroupDataDTO> findBySgrpId(Long sgrpId, Integer flag) {
        SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
        subgroupDataSelectionDTO.setDbType(InitConfig.getDriverType());
        subgroupDataSelectionDTO.setF_SGRP(sgrpId);
        subgroupDataSelectionDTO.setF_FLAG(flag);
        if (subgroupDataSelectionDTO.getTotalNum() == null) {
            subgroupDataSelectionDTO.setTotalNum(1000);
        }
        List<SubgroupDataDTO> subgroupDataDTOList = baseMapper.getSubgroupDataDTOList(subgroupDataSelectionDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) return subgroupDataDTOList;
        R<EMPL_INF_DTO> info = remoteUserService.info(subgroupDataDTOList.get(0).getF_CRUE());
        EMPL_INF_DTO emplInfDto = info.getData();
        subgroupDataDTOList.forEach(dataDTO -> {
            dataDTO.setCreateName(emplInfDto.getF_NAME());
        });
        subgroupDataDTOList = structure(subgroupDataDTOList, subgroupDataSelectionDTO, false);
        subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);

        return subgroupDataDTOList;
    }

}




