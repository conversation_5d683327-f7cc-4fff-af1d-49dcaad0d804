package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.*;
import com.yingfei.dataManagement.mapper.convert.SubgroupDataConvert;
import com.yingfei.dataManagement.service.INSPECTION_TYPE_DATService;
import com.yingfei.dataManagement.service.PART_REVService;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.ParetoService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.AnalyseTypeDTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.ParetoConfigDTO;
import com.yingfei.entity.dto.chart.ParetoDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.enums.TimeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class ParetoServiceImpl implements ParetoService {

    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private SHIFT_GRPMapper shiftGrpMapper;
    @Resource
    private SHIFT_DATMapper shiftDatMapper;
    @Resource
    private TEST_INFMapper testInfMapper;
    @Resource
    private PART_INFMapper partInfMapper;
    @Resource
    private JOB_DATMapper jobDatMapper;
    @Resource
    private JOB_GRPMapper jobGrpMapper;
    @Resource
    private PRCS_INFMapper prcsInfMapper;
    @Resource
    private SGRP_DSCMapper sgrpDscMapper;
    @Resource
    private DESC_DATMapper descDatMapper;
    @Resource
    private LOT_INFMapper lotInfMapper;
    @Resource
    private EMPL_INFMapper emplInfMapper;
    @Resource
    private DEF_GRPMapper defGrpMapper;
    @Resource
    private DEF_DATMapper defDatMapper;
    @Resource
    private EVNT_INFMapper evntInfMapper;
    @Resource
    private ROOT_CAUSE_DATMapper rootCauseDatMapper;
    @Resource
    private ROOT_CAUSE_GRPMapper rootCauseGrpMapper;
    @Resource
    private RESPONSE_ACTION_DATMapper responseActionDatMapper;
    @Resource
    private RESPONSE_ACTION_GRPMapper responseActionGrpMapper;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private SGRP_INFMapper sgrpInfMapper;
    @Resource
    private INSPECTION_TYPE_DATService inspectionTypeDatService;
    @Resource
    private PART_REVService partRevService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private RedisService redisService;

    @Override
    public List<ParetoDTO> getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.PARETO_DIAGRAM);

        ParetoConfigDTO paretoConfigDTO = new ParetoConfigDTO();
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            paretoConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), ParetoConfigDTO.class);
        } else {
            Object chartConfig = analysisChartConfigDTO.getChartConfig();
            paretoConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(chartConfig), ParetoConfigDTO.class);
        }
        List<ParetoDTO> paretoDTOList = new ArrayList<>();
        /*获取聚合分析子组数据*/
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return paretoDTOList;


        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            return paretoDTOList;
        }
        subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);

        return getParetoDTOS(subgroupDataSelectionDTO, paretoConfigDTO, subgroupDataDTOList, analysisChartConfigDTO);
    }

    private List<ParetoDTO> getParetoDTOS(SubgroupDataSelectionDTO subgroupDataSelectionDTO, ParetoConfigDTO paretoConfigDTO, List<SubgroupDataDTO> subgroupDataDTOList, AnalysisChartConfigDTO analysisChartConfigDTO) {

        subgroupDataDTOList = chartCommonService.getTotalNumSubgroup(subgroupDataDTOList, subgroupDataSelectionDTO.getMaxNum(),2);
        List<ParetoDTO> paretoDTOList;
        if (paretoConfigDTO.getType() == 1) {
            /*获取子组关联过程事件*/
            if (CollectionUtils.isNotEmpty(subgroupDataDTOList)) {
//                List<EVNT_INF> evntInfList = getEvntInfList(subgroupDataDTOList.stream().map(SubgroupDataDTO::getF_SGRP).collect(Collectors.toSet()));
                /*未通过测试条件筛选 需要在逻辑中进行二次筛选*/
                List<EVNT_INF> evntInfList = sgrpInfMapper.getEvntInfList(subgroupDataSelectionDTO);
                evntInfList = evntInfList.stream().filter(s -> CollectionUtils.isEmpty(subgroupDataSelectionDTO.getTestList()) ?
                        s.getF_TEST().equals(subgroupDataSelectionDTO.getF_TEST()) : subgroupDataSelectionDTO.getTestList().contains(s.getF_TEST())).collect(Collectors.toList());
                Map<Long, List<EVNT_INF>> evntMap = evntInfList.stream()
                        .collect(Collectors.groupingBy(EVNT_INF::getF_SGRP,
                                LinkedHashMap::new, Collectors.toList()));
                subgroupDataDTOList.forEach(subgroupDataDTO -> {
                    if (CollectionUtils.isNotEmpty(evntMap.get(subgroupDataDTO.getF_SGRP()))) {
                        if (subgroupDataDTO.getEvntInfList() == null) {
                            subgroupDataDTO.setEvntInfList(evntMap.get(subgroupDataDTO.getF_SGRP()));
                        } else {
                            subgroupDataDTO.getEvntInfList().addAll(evntMap.get(subgroupDataDTO.getF_SGRP()));
                        }
                    }
                });
            }
            //todo 通过条件去过程事件表查询 再通过子组id查对应子组

//            subgroupDataDTOList = get();
        }
        paretoDTOList = getData(subgroupDataDTOList, paretoConfigDTO, analysisChartConfigDTO);
        if (paretoConfigDTO.getCalculateType() == 2) {
            /*加权处理*/
            weightedApproach(paretoDTOList, paretoConfigDTO);
        }
        return paretoDTOList;
    }

    @Override
    public List<ParetoDTO> getInfoList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.PARETO_DIAGRAM);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }

        ParetoConfigDTO paretoConfigDTO = new ParetoConfigDTO();
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            paretoConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), ParetoConfigDTO.class);
        } else {
            Object chartConfig = analysisChartConfigDTO.getChartConfig();
            paretoConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(chartConfig), ParetoConfigDTO.class);
        }
        List<ParetoDTO> paretoDTOList = new ArrayList<>();
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return paretoDTOList;

        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);

        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            return paretoDTOList;
        }
        /*多测试拆分*/
        subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
        /*过滤对应的条件*/
        SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO = subgroupDataSelectionDTO;
        subgroupDataDTOList = subgroupDataDTOList.stream().filter(s ->
                        finalSubgroupDataSelectionDTO.getPartList().contains(s.getF_PART()) &&
                                finalSubgroupDataSelectionDTO.getPtrvList().contains(s.getF_REV()) &&
                                finalSubgroupDataSelectionDTO.getPrcsList().contains(s.getF_PRCS()) &&
                                finalSubgroupDataSelectionDTO.getTestList().contains(s.getF_TEST()))
                .collect(Collectors.toList());

        return getParetoDTOS(subgroupDataSelectionDTO, paretoConfigDTO, subgroupDataDTOList, analysisChartConfigDTO);
    }


    /**
     * 加权处理
     *
     * @param paretoDTOList
     * @param paretoConfigDTO
     */
    private void weightedApproach(List<ParetoDTO> paretoDTOList, ParetoConfigDTO paretoConfigDTO) {
        /*获取分析角度最下级*/
        int size = paretoConfigDTO.getAnalyseTypeMap().size();
        for (int i = size; i > 0; i--) {
            /*获取对应层级*/
            int finalI = i;
            paretoDTOList.forEach(paretoDTO -> {
                recursion(paretoDTO.getParetoDTOChildList(), finalI, size, paretoDTO);
            });
        }
    }

    /**
     * 加权递归
     *
     * @param paretoDTOList
     * @param i
     * @param size
     * @param pareto
     */
    private void recursion(List<ParetoDTO> paretoDTOList, int i, int size, ParetoDTO pareto) {
        if (i == 1) {
            if (CollectionUtils.isNotEmpty(paretoDTOList)) {
                Double sum = paretoDTOList.stream().map(ParetoDTO::getNum).reduce(Double::sum).orElse(0d);
                pareto.setNum(sum);
                /*下级数值之和 除以 频数*/
                pareto.setFactor(BigDecimal.valueOf(sum).divide(BigDecimal.valueOf(pareto.getFrequency()), 4, RoundingMode.DOWN).doubleValue());
            }
        } else {
            paretoDTOList.forEach(paretoDTO -> {
                if (paretoDTO.getLevel() != i) {
                    recursion(paretoDTO.getParetoDTOChildList(), i, size, paretoDTO);
                    return;
                }
                if (i == size) {
                    /*获取对应因子*/
                    Double factor = getFactor(paretoDTO.getLevelType(), Long.valueOf(paretoDTO.getId()));
                    paretoDTO.setNum(BigDecimal.valueOf(paretoDTO.getNum()).multiply(BigDecimal.valueOf(factor)).doubleValue());
                    paretoDTO.setFactor(factor);
                } else {
                    /*获取下级数值之和*/
                    if (CollectionUtils.isNotEmpty(paretoDTO.getParetoDTOChildList())) {
                        Double sum = paretoDTO.getParetoDTOChildList().stream().map(ParetoDTO::getNum).reduce(Double::sum).orElse(0d);
                        paretoDTO.setNum(sum);
                        /*下级数值之和 除以 频数*/
                        paretoDTO.setFactor(BigDecimal.valueOf(sum).divide(BigDecimal.valueOf(paretoDTO.getFrequency()), 4, RoundingMode.DOWN).doubleValue());
                    }
                }
            });
        }

    }

    /**
     * 数据组装
     *
     * @param subgroupDataDTOList
     * @param paretoConfigDTO
     * @param analysisChartConfigDTO
     * @return
     */
    public List<ParetoDTO> getData(List<SubgroupDataDTO> subgroupDataDTOList, ParetoConfigDTO paretoConfigDTO, AnalysisChartConfigDTO analysisChartConfigDTO) {
        if (paretoConfigDTO == null) {
            paretoConfigDTO = ParetoConfigDTO.initList();
        }
        Map<Integer, AnalyseTypeDTO> analyseTypeMap = paretoConfigDTO.getAnalyseTypeMap();

        List<ParetoDTO> list = new ArrayList<>();

        AnalyseTypeDTO levelOne = analyseTypeMap.get(1);

        if (levelOne == null)
            throw new BaseException(I18nUtils.getMessage("HIERARCHY_PARSING_ERROR"));
        if (paretoConfigDTO.getActualValueType() != 1) {
            subgroupDataDTOList = reassembly(subgroupDataDTOList);
        }
        Map<String, List<SubgroupDataDTO>> oneMap = getList(subgroupDataDTOList, levelOne, paretoConfigDTO);

        ParetoConfigDTO finalParetoConfigDTO = paretoConfigDTO;
        AtomicReference<Double> sum = new AtomicReference<>(0d);
        oneMap.forEach((key, value) -> {
            /*实际值类型*/
            if (finalParetoConfigDTO.getType() == 1) {
                Set<EVNT_INF> collect = value.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toSet());
                collect.forEach(evntInf -> {
                    if (StringUtils.isNotEmpty(evntInf.getF_DATA())) {
                        JSONArray array = JSONArray.parseArray(evntInf.getF_DATA());
                        //todo 后续一个配置项看是否将evntInf.getF_DATA()拆分
                        sum.updateAndGet(v -> v + array.size());
                    }
                });
            } else {
                /*抽样数据将失效子组排除*/
                List<SubgroupDataDTO> collect = value.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());
                if (finalParetoConfigDTO.getActualValueType() == 1 || finalParetoConfigDTO.getActualValueType() == 2) {
                    sum.updateAndGet(v -> v + collect.size());
                } else {
                    Double aDouble = collect.stream().map(SubgroupDataDTO::getTestValDto).map(SGRP_VAL_CHILD_DTO.Test::getTestVal).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                    sum.updateAndGet(v -> v + aDouble);
                }
            }
        });

        oneMap.forEach((k, v) -> {
            ParetoDTO paretoDTO = new ParetoDTO();
            paretoDTO.setLevel(1);
            paretoDTO.setLevelType(levelOne.getAnalyseType());
            paretoDTO.setSum(sum.get());
            paretoDTO.setId(k);
            if (finalParetoConfigDTO.getActualValueType() == 1 || finalParetoConfigDTO.getActualValueType() == 2) {
                paretoDTO.setFrequency((double) v.size());
            } else {
                Double aDouble = v.stream().map(SubgroupDataDTO::getTestValDto).map(SGRP_VAL_CHILD_DTO.Test::getTestVal).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                paretoDTO.setFrequency(aDouble);
            }
            if (finalParetoConfigDTO.getType() == 1) {
                Set<EVNT_INF> collect = v.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toSet());
                paretoDTO.setNum((double) collect.size());
            } else {
                if (finalParetoConfigDTO.getActualValueType() == 1 || finalParetoConfigDTO.getActualValueType() == 2) {
                    paretoDTO.setNum((double) v.size());
                } else {
                    Double aDouble = v.stream().map(SubgroupDataDTO::getTestValDto).map(SGRP_VAL_CHILD_DTO.Test::getTestVal).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                    paretoDTO.setNum(aDouble);
                }
            }
            if (sum.get() == 0) {
                paretoDTO.setProportion(0d);
            } else {
                paretoDTO.setProportion(BigDecimal.valueOf(paretoDTO.getNum()).divide(BigDecimal.valueOf(sum.get()), 6, RoundingMode.DOWN).doubleValue());
            }
            getParetoDTO(paretoDTO, finalParetoConfigDTO, levelOne, k, v, analysisChartConfigDTO);
            list.add(paretoDTO);
        });
        list.sort(new Comparator<ParetoDTO>() {
            public int compare(ParetoDTO o1, ParetoDTO o2) {
                Double s1 = o1.getNum();
                Double s2 = o2.getNum();
                //该排序为正序排序，如果倒序排序则将compare中的s2和s1互换位置
                return s2.compareTo(s1);
            }
        });
        return list;
    }

    /**
     * 递归
     *
     * @param paretoDTO
     * @param paretoConfigDTO
     * @return
     */
    public void initData(ParetoDTO paretoDTO, ParetoConfigDTO paretoConfigDTO, List<SubgroupDataDTO> extList,
                         List<ParetoDTO> list, AnalysisChartConfigDTO analysisChartConfigDTO) {
        Map<Integer, AnalyseTypeDTO> analyseTypeMap = paretoConfigDTO.getAnalyseTypeMap();
        AnalyseTypeDTO analyseTypeDTO = analyseTypeMap.get(paretoDTO.getLevel());
        if (analyseTypeDTO == null) {
            return;
        }

        Map<String, List<SubgroupDataDTO>> map = getList(extList, analyseTypeDTO, paretoConfigDTO);
        AtomicReference<Double> sum = new AtomicReference<>(0d);
        map.forEach((key, value) -> {
            /*实际值类型*/
            if (paretoConfigDTO.getType() == 1) {
                Set<EVNT_INF> collect = value.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toSet());
                collect.forEach(evntInf -> {
                    if (StringUtils.isNotEmpty(evntInf.getF_DATA())) {
                        JSONArray array = JSONArray.parseArray(evntInf.getF_DATA());
                        sum.updateAndGet(v -> v + array.size());
                    }
                });
            } else {
                if (paretoConfigDTO.getActualValueType() == 1 || paretoConfigDTO.getActualValueType() == 2) {
                    sum.updateAndGet(v -> v + value.size());
                } else {
                    Double aDouble = value.stream().map(SubgroupDataDTO::getTestValDto).map(SGRP_VAL_CHILD_DTO.Test::getTestVal).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                    sum.updateAndGet(v -> v + aDouble);
                }
            }
        });
        map.forEach((k, v) -> {
            ParetoDTO dataDTO = new ParetoDTO();
            dataDTO.setLevel(paretoDTO.getLevel());
            dataDTO.setLevelType(analyseTypeDTO.getAnalyseType());
            dataDTO.setSum(sum.get());
            dataDTO.setId(k);
            if (paretoConfigDTO.getActualValueType() == 1 || paretoConfigDTO.getActualValueType() == 2) {
                dataDTO.setFrequency((double) v.size());
            } else {
                Double aDouble = v.stream().map(SubgroupDataDTO::getTestValDto).map(SGRP_VAL_CHILD_DTO.Test::getTestVal).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                dataDTO.setFrequency(aDouble);
            }

            if (paretoConfigDTO.getType() == 1) {
                Set<EVNT_INF> collect = v.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toSet());
                dataDTO.setNum((double) collect.size());
            } else {
                if (paretoConfigDTO.getActualValueType() == 1 || paretoConfigDTO.getActualValueType() == 2) {
                    dataDTO.setNum((double) v.size());
                } else {
                    Double aDouble = v.stream().map(SubgroupDataDTO::getTestValDto).map(SGRP_VAL_CHILD_DTO.Test::getTestVal).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                    dataDTO.setNum(aDouble);
                }
            }
            if (sum.get() == 0) {
                dataDTO.setProportion(0d);
            } else {
                dataDTO.setProportion(BigDecimal.valueOf(dataDTO.getNum()).divide(BigDecimal.valueOf(sum.get()), 6, RoundingMode.DOWN).doubleValue());
            }
            dataDTO = getParetoDTO(dataDTO, paretoConfigDTO, analyseTypeDTO, k, v, analysisChartConfigDTO);
            list.add(dataDTO);
        });


    }

    private ParetoDTO getParetoDTO(ParetoDTO paretoDTO, ParetoConfigDTO paretoConfigDTO,
                                   AnalyseTypeDTO level, String k, List<SubgroupDataDTO> v, AnalysisChartConfigDTO analysisChartConfigDTO) {
        ParetoDTO dataDTO = new ParetoDTO();
        dataDTO.setName(I18nUtils.getMessage("NOT_EXIST"));
        dataDTO.setLevel(paretoDTO.getLevel() + 1);
        Map<Integer, AnalyseTypeDTO> analyseTypeMap = paretoConfigDTO.getAnalyseTypeMap();
        AnalyseTypeDTO analyseTypeDTO = analyseTypeMap.get(paretoDTO.getLevel());
        paretoDTO.setName(getName(analyseTypeDTO, k));

        List<ParetoDTO> list = new ArrayList<>();
        initData(dataDTO, paretoConfigDTO, v, list, analysisChartConfigDTO);
        list.sort(new Comparator<ParetoDTO>() {
            public int compare(ParetoDTO o1, ParetoDTO o2) {
                Double s1 = o1.getNum();
                Double s2 = o2.getNum();
                //该排序为正序排序，如果倒序排序则将compare中的s2和s1互换位置
                return s2.compareTo(s1);
            }
        });
        paretoDTO.setParetoDTOChildList(list);
        return paretoDTO;
    }

    public Map<String, List<SubgroupDataDTO>> getList(List<SubgroupDataDTO> subgroupDataDTOList, AnalyseTypeDTO analyseTypeDTO, ParetoConfigDTO paretoConfigDTO) {
        Map<String, List<SubgroupDataDTO>> map = new HashMap<>();
        switch (ParetoAnalyseTypeEnum.getByCode(analyseTypeDTO.getAnalyseType())) {
            case SHIFT_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_SHIFT() != null && s.getF_SHIFT() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_SHIFT().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case SHIFT_GRP:
                /*获取对应班次*/
                map = subgroupDataDTOList.stream().filter(s -> s.getF_SHIFT() != null && s.getF_SHIFT() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_SHIFT().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                List<SHIFT_DAT> shiftDats;
                if (MapUtils.isEmpty(map)) {
                    shiftDats = new ArrayList<>();
                } else {
                    LambdaQueryWrapper<SHIFT_DAT> shift = new LambdaQueryWrapper<>();
                    shift.in(SHIFT_DAT::getF_SHIFT, map.keySet());
                    shiftDats = shiftDatMapper.selectList(shift);
                }
                Map<String, List<SubgroupDataDTO>> shiftGrpMap = new HashMap<>();
                Map<String, List<SubgroupDataDTO>> shiftGrpExtList = map;
                shiftDats.forEach(shftDat -> {
                    if (shiftGrpMap.get(shftDat.getF_SHGP().toString()) == null) {
                        shiftGrpMap.put(shftDat.getF_SHGP().toString(), shiftGrpExtList.get(shftDat.getF_SHIFT().toString()));
                    } else {
                        shiftGrpMap.get(shftDat.getF_SHGP().toString()).addAll(shiftGrpExtList.get(shftDat.getF_SHIFT().toString()));
                    }
                });

                map = shiftGrpMap;
                break;
            case TEST_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_TEST() != null && s.getF_TEST() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_TEST().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case TEST_NO:
//                map = subgroupDataDTOList.stream().filter(s -> s.getSgrpValDto().getSgrpValChildDto().getF_TSNO() != null)
//                        .collect(Collectors.groupingBy(s -> s.getF_TSNO().toString(),
//                                LinkedHashMap::new, Collectors.toList()));
                break;
            case PART_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_PART() != null && s.getF_PART() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_PART().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case JOB_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_JOB() != null && s.getF_JOB() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_JOB().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case JOB_GRP:
                /*获取对应工作*/
                map = subgroupDataDTOList.stream().filter(s -> s.getF_JOB() != null && s.getF_JOB() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_JOB().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                List<JOB_DAT> jobDats;
                if (MapUtils.isEmpty(map)) {
                    jobDats = new ArrayList<>();
                } else {
                    LambdaQueryWrapper<JOB_DAT> jobDatLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    jobDatLambdaQueryWrapper.in(JOB_DAT::getF_JOB, map.keySet());
                    jobDats = jobDatMapper.selectList(jobDatLambdaQueryWrapper);
                }
                Map<String, List<SubgroupDataDTO>> jobGrpMap = new HashMap<>();
                Map<String, List<SubgroupDataDTO>> jobGrpExtList = map;
                jobDats.forEach(jobDat -> {
                    if (jobGrpMap.get(jobDat.getF_JBGP().toString()) == null) {
                        jobGrpMap.put(jobDat.getF_JBGP().toString(), jobGrpExtList.get(jobDat.getF_JOB().toString()));
                    } else {
                        jobGrpMap.get(jobDat.getF_JBGP().toString()).addAll(jobGrpExtList.get(jobDat.getF_JOB().toString()));
                    }
                });

                map = jobGrpMap;
                break;
            case PRCS_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_PRCS() != null && s.getF_PRCS() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_PRCS().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case DESC_DAT:
                /*获取描述符组对应子组*/
                LambdaQueryWrapper<SGRP_DSC> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SGRP_DSC::getF_DSGP, analyseTypeDTO.getGroupId());
                List<SGRP_DSC> sgrpDscs = sgrpDscMapper.selectList(queryWrapper);
                Map<String, List<SGRP_DSC>> sgrpMap = sgrpDscs.stream().collect(Collectors.groupingBy(s -> s.getF_DESC().toString(),
                        LinkedHashMap::new, Collectors.toList()));
                Map<String, List<SubgroupDataDTO>> descMap = new HashMap<>();
                List<SubgroupDataDTO> finalSubgroupDataDTOList = subgroupDataDTOList;
                sgrpMap.forEach((k, v) -> {
                    List<Long> list = v.stream().map(SGRP_DSC::getF_SGRP).collect(Collectors.toList());
                    List<SubgroupDataDTO> exts = finalSubgroupDataDTOList.stream().filter(s -> list.contains(s.getF_SGRP())).collect(Collectors.toList());
                    descMap.put(k, exts);
                });
                map = descMap;
                break;
            case LOT_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_LOT() != null && s.getF_LOT() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_LOT().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case TIME:
                /*获取最大和最小子组时间*/
                int max = (int) (subgroupDataDTOList.stream().mapToLong(s -> s.getF_SGTM().getTime()).max().orElse(0L) / 1000);
                int min = (int) (subgroupDataDTOList.stream().mapToLong(s -> s.getF_SGTM().getTime()).min().orElse(0L) / 1000);
                if (max == 0L || min == 0L) break;
                switch (TimeEnum.getType(analyseTypeDTO.getTimeType())) {
                    case SECOND:
                        for (long i = 0; ; i += analyseTypeDTO.getTimeInterval()) {
                            long start = min + i * analyseTypeDTO.getTimeInterval();
                            long end = min + (i + 1) * analyseTypeDTO.getTimeInterval();
                            if (start > (long) max) break;
                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(String.valueOf(start), collect);
                        }
                        break;
                    case MINUTE:
                        int maxMinute = (max + analyseTypeDTO.getTimeInterval() * 60) / 60;
                        int minMinute = (min - analyseTypeDTO.getTimeInterval() * 60) / 60;
                        for (int i = minMinute; i < maxMinute; i += analyseTypeDTO.getTimeInterval()) {
                            long start = i * 60L;
                            long end = (i + analyseTypeDTO.getTimeInterval()) * 60L;
                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(String.valueOf(start), collect);
                        }
                        break;
                    case HOUR:
                        int maxHour = (max + analyseTypeDTO.getTimeInterval() * 3600) / 3600;
                        int minHour = (min - analyseTypeDTO.getTimeInterval() * 3600) / 3600;
                        for (int i = minHour; i < maxHour; i += analyseTypeDTO.getTimeInterval()) {
                            long start = i * 3600L;
                            long end = (i + analyseTypeDTO.getTimeInterval()) * 3600L;
                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(String.valueOf(start), collect);
                        }
                        break;
                    case DAY:
                        int maxDay = (max + analyseTypeDTO.getTimeInterval() * 86400) / 86400;
                        int minDay = (min - analyseTypeDTO.getTimeInterval() * 86400) / 86400;
                        for (int i = minDay; i < maxDay; i += analyseTypeDTO.getTimeInterval()) {
                            long start = i * 86400L;
                            long end = (i + analyseTypeDTO.getTimeInterval()) * 86400L;
                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(String.valueOf(start), collect);
                        }
                        break;
                    case WEEK:
                        Date weekDate = new Date(min * 1000L);
                        Calendar weekCalendar = Calendar.getInstance();
                        weekCalendar.setTime(weekDate);
                        weekCalendar.set(Calendar.HOUR_OF_DAY, 0);
                        weekCalendar.set(Calendar.MINUTE, 0);
                        weekCalendar.set(Calendar.SECOND, 0);
                        // 设置到周一
                        weekCalendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                        for (int i = 0; ; i++) {
                            long start = weekCalendar.getTimeInMillis() / 1000L;
                            if (start > max) break;
                            /*加一周*/
                            weekCalendar.add(Calendar.WEEK_OF_YEAR, analyseTypeDTO.getTimeInterval());
                            Date nextTime = weekCalendar.getTime();
                            weekCalendar.setTime(nextTime);
                            long end = nextTime.getTime() / 1000L;

                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(String.valueOf(start), collect);
                        }
                        break;
                    case MONTH:
                        Date monthDate = new Date(min * 1000L);
                        Calendar monthCalendar = Calendar.getInstance();
                        monthCalendar.setTime(monthDate);
                        monthCalendar.set(Calendar.HOUR_OF_DAY, 0);
                        monthCalendar.set(Calendar.MINUTE, 0);
                        monthCalendar.set(Calendar.SECOND, 0);
                        /*设置日为1*/
                        monthCalendar.set(Calendar.DAY_OF_MONTH, 1);
                        for (int i = 0; ; i++) {
                            long start = monthCalendar.getTimeInMillis() / 1000L;
                            if (start > max) break;
                            /*加一个月*/
                            monthCalendar.add(Calendar.MONTH, analyseTypeDTO.getTimeInterval());
                            Date nextTime = monthCalendar.getTime();
                            monthCalendar.setTime(nextTime);
                            long end = nextTime.getTime() / 1000L;

                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(String.valueOf(start), collect);
                        }
                        break;
                    case QUARTER:
                        Date quarterDate = new Date(min * 1000L);
                        Calendar quarterCalendar = Calendar.getInstance();
                        quarterCalendar.setTime(quarterDate);
                        quarterCalendar.set(Calendar.HOUR_OF_DAY, 0);
                        quarterCalendar.set(Calendar.MINUTE, 0);
                        quarterCalendar.set(Calendar.SECOND, 0);
                        // 获取当前月份（月份从0开始，所以需要加1）
                        int month = quarterCalendar.get(Calendar.MONTH) + 1;
                        /*获取季度开始月份*/
                        double ceil = Math.ceil(month / 3D);
                        int quarterStartMonth = (int) (ceil * 3) - 2;
                        quarterCalendar.set(Calendar.MONTH, quarterStartMonth);
                        quarterCalendar.set(Calendar.DAY_OF_MONTH, 1);
                        for (int i = 0; ; i++) {
                            long start = quarterCalendar.getTimeInMillis() / 1000L;
                            if (start > max) break;
                            /*加一个季度*/
                            quarterCalendar.add(Calendar.MONTH, 3 * analyseTypeDTO.getTimeInterval());
                            Date nextTime = quarterCalendar.getTime();
                            quarterCalendar.setTime(nextTime);
                            long end = nextTime.getTime() / 1000L;

                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(String.valueOf(start), collect);
                        }
                }
                break;
            case STAFF:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_CRUE() != null && s.getF_CRUE() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_CRUE().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case DEF_GRP:
                /*根据测试值重新组装子组*/
                subgroupDataDTOList = reassembly(subgroupDataDTOList);
                if (paretoConfigDTO.getActualValueType() ==3) {
                    map = subgroupDataDTOList.stream().filter(s -> s.getTestValDto().getDefectGrpId() != null && s.getTestValDto().getDefectGrpId() != 0L)
                            .collect(Collectors.groupingBy(s -> s.getTestValDto().getDefectGrpId().toString(),
                                    LinkedHashMap::new, Collectors.toList()));
                } else {
                    List<SubgroupDataDTO> collect =
                            subgroupDataDTOList.stream().filter(s -> s.getTestValDto().getDefectGrpId() == null || s.getTestValDto().getDefectGrpId() == 0L)
                                    .collect(Collectors.toList());
                    map.put(Constants.NaN, collect);
                }
                break;
            case DEF_DAT:
                subgroupDataDTOList = reassembly(subgroupDataDTOList);
                if (paretoConfigDTO.getActualValueType() == 3) {
                    map = subgroupDataDTOList.stream().filter(s -> s.getTestValDto().getDefectId() != null && s.getTestValDto().getDefectId() != 0L)
                            .collect(Collectors.groupingBy(s -> s.getTestValDto().getDefectId().toString(),
                                    LinkedHashMap::new, Collectors.toList()));
                } else {
                    List<SubgroupDataDTO> list =
                            subgroupDataDTOList.stream().filter(s -> s.getTestValDto().getDefectId() == null || s.getTestValDto().getDefectId() == 0L)
                                    .collect(Collectors.toList());
                    map.put(Constants.NaN, list);
                }
                break;
            case EVENT_TYPE:
                List<EVNT_INF> typeEvntInfList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                Map<String, List<SubgroupDataDTO>> typeMap = subgroupDataDTOList.stream().collect(Collectors.groupingBy(s -> s.getF_SGRP().toString(),
                        LinkedHashMap::new, Collectors.toList()));
                Map<String, List<SubgroupDataDTO>> hashMap = new HashMap<>();
                typeEvntInfList.forEach(evntInf -> {
                    if (StringUtils.isNotEmpty(evntInf.getF_DATA())) {
                        JSONArray array = JSONArray.parseArray(evntInf.getF_DATA());
                        Set<String> set = new HashSet<>();
                        array.forEach(s -> {
                            JSONObject jsonObject = JSONObject.parseObject(s.toString());
                            set.add(jsonObject.get("type").toString());
                        });
                        set.forEach(t -> {
                            List<SubgroupDataDTO> dataDTOS = hashMap.get(t);
                            if (CollectionUtils.isNotEmpty(dataDTOS)) {
                                HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                                hashSet.addAll(typeMap.get(evntInf.getF_SGRP().toString()));
                                hashMap.put(t, new ArrayList<>(hashSet));
                            } else {
                                hashMap.put(t, typeMap.get(evntInf.getF_SGRP().toString()));
                            }
                        });
                    }
                });
                map = hashMap;
                break;
            case EVENT_NAME:
                List<EVNT_INF> nameEvntInfList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                Map<String, List<SubgroupDataDTO>> nameMap = subgroupDataDTOList.stream().collect(Collectors.groupingBy(s -> s.getF_SGRP().toString(),
                        LinkedHashMap::new, Collectors.toList()));
                Map<String, List<SubgroupDataDTO>> nameHashMap = new HashMap<>();
                nameEvntInfList.forEach(evntInf -> {
                    if (StringUtils.isNotEmpty(evntInf.getF_DATA())) {
                        JSONArray array = JSONArray.parseArray(evntInf.getF_DATA());
                        Set<String> set = new HashSet<>();
                        array.forEach(s -> {
                            JSONObject jsonObject = JSONObject.parseObject(s.toString());
                            if (jsonObject.get("chart") != null) {
                                set.add(jsonObject.get("chart").toString() + ":" + jsonObject.get("name").toString());
                            } else {
                                set.add(jsonObject.get("name").toString());
                            }
                        });
                        set.forEach(t -> {
                            List<SubgroupDataDTO> dataDTOS = nameHashMap.get(t);
                            if (CollectionUtils.isNotEmpty(dataDTOS)) {
                                HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                                hashSet.addAll(nameMap.get(evntInf.getF_SGRP().toString()));
                                nameHashMap.put(t, new ArrayList<>(hashSet));
                            } else {
                                nameHashMap.put(t, nameMap.get(evntInf.getF_SGRP().toString()));
                            }
                        });
                    }
                });
                map = nameHashMap;
                break;
            case CAUSE_DAT:
                List<EVNT_INF> causeDatEvntInfList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                Map<String, List<SubgroupDataDTO>> causeDatMap = subgroupDataDTOList.stream().collect(Collectors.groupingBy(s -> s.getF_SGRP().toString(),
                        LinkedHashMap::new, Collectors.toList()));
                Map<String, List<SubgroupDataDTO>> causeDatHashMap = new HashMap<>();
                causeDatEvntInfList.forEach(evntInf -> {
                    if (evntInf.getF_RTCS() == 0L) {
                        List<SubgroupDataDTO> dataDTOS = causeDatHashMap.get(Constants.NaN);
                        if (CollectionUtils.isNotEmpty(dataDTOS)) {
                            HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                            hashSet.addAll(causeDatMap.get(evntInf.getF_SGRP().toString()));
                            causeDatHashMap.put(Constants.NaN, new ArrayList<>(hashSet));
                        } else {
                            causeDatHashMap.put(Constants.NaN, causeDatMap.get(evntInf.getF_SGRP().toString()));
                        }
                    } else {
                        List<SubgroupDataDTO> dataDTOS = causeDatHashMap.get(evntInf.getF_RTCS().toString());
                        if (CollectionUtils.isNotEmpty(dataDTOS)) {
                            HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                            hashSet.addAll(causeDatMap.get(evntInf.getF_SGRP().toString()));
                            causeDatHashMap.put(evntInf.getF_RTCS().toString(), new ArrayList<>(hashSet));
                        } else {
                            causeDatHashMap.put(evntInf.getF_RTCS().toString(), causeDatMap.get(evntInf.getF_SGRP().toString()));
                        }
                    }
                });
                map = causeDatHashMap;
                break;
            case CAUSE_GRP:
                List<EVNT_INF> causeGrpEvntInfList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                Map<String, List<SubgroupDataDTO>> causeGrpMap = subgroupDataDTOList.stream().collect(Collectors.groupingBy(s -> s.getF_SGRP().toString(),
                        LinkedHashMap::new, Collectors.toList()));
                Map<String, List<SubgroupDataDTO>> causeGrpHashMap = new HashMap<>();
                causeGrpEvntInfList.forEach(evntInf -> {
                    if (evntInf.getF_RTCS() == 0L) {
                        List<SubgroupDataDTO> dataDTOS = causeGrpHashMap.get(Constants.NaN);
                        if (CollectionUtils.isNotEmpty(dataDTOS)) {
                            HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                            hashSet.addAll(causeGrpMap.get(evntInf.getF_SGRP().toString()));
                            causeGrpHashMap.put(Constants.NaN, new ArrayList<>(hashSet));
                        } else {
                            causeGrpHashMap.put(Constants.NaN, causeGrpMap.get(evntInf.getF_SGRP().toString()));
                        }
                    } else {
                        /*获取异常原因组*/
                        ROOT_CAUSE_DAT rootCauseDat = rootCauseDatMapper.selectById(evntInf.getF_RTCS());
                        List<SubgroupDataDTO> dataDTOS = causeGrpHashMap.get(rootCauseDat.getF_RCGP().toString());
                        if (CollectionUtils.isNotEmpty(dataDTOS)) {
                            HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                            hashSet.addAll(causeGrpMap.get(evntInf.getF_SGRP().toString()));
                            causeGrpHashMap.put(rootCauseDat.getF_RCGP().toString(), new ArrayList<>(hashSet));
                        } else {
                            causeGrpHashMap.put(rootCauseDat.getF_RCGP().toString(), causeGrpMap.get(evntInf.getF_SGRP().toString()));
                        }
                    }
                });
                map = causeGrpHashMap;
                break;
            case RESPONSE_ACTION_DAT:
                List<EVNT_INF> responseActionDatEvntInfList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                Map<String, List<SubgroupDataDTO>> responseActionDatMap = subgroupDataDTOList.stream().collect(Collectors.groupingBy(s -> s.getF_SGRP().toString(),
                        LinkedHashMap::new, Collectors.toList()));
                Map<String, List<SubgroupDataDTO>> responseActionDatHashMap = new HashMap<>();
                responseActionDatEvntInfList.forEach(evntInf -> {
                    if (evntInf.getF_RSAT() == 0L) {
                        List<SubgroupDataDTO> dataDTOS = responseActionDatHashMap.get(Constants.NaN);
                        if (CollectionUtils.isNotEmpty(dataDTOS)) {
                            HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                            hashSet.addAll(responseActionDatMap.get(evntInf.getF_SGRP().toString()));
                            responseActionDatHashMap.put(Constants.NaN, new ArrayList<>(hashSet));
                        } else {
                            responseActionDatHashMap.put(Constants.NaN, responseActionDatMap.get(evntInf.getF_SGRP().toString()));
                        }
                    } else {
                        List<SubgroupDataDTO> dataDTOS = responseActionDatHashMap.get(evntInf.getF_RSAT().toString());
                        if (CollectionUtils.isNotEmpty(dataDTOS)) {
                            HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                            hashSet.addAll(responseActionDatMap.get(evntInf.getF_SGRP().toString()));
                            responseActionDatHashMap.put(evntInf.getF_RSAT().toString(), new ArrayList<>(hashSet));
                        } else {
                            responseActionDatHashMap.put(evntInf.getF_RSAT().toString(), responseActionDatMap.get(evntInf.getF_SGRP().toString()));
                        }
                    }
                });
                map = responseActionDatHashMap;
                break;
            case RESPONSE_ACTION_GRP:
                List<EVNT_INF> responseActionGrpEvntInfList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getEvntInfList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                Map<String, List<SubgroupDataDTO>> responseActionGrpMap = subgroupDataDTOList.stream().collect(Collectors.groupingBy(s -> s.getF_SGRP().toString(),
                        LinkedHashMap::new, Collectors.toList()));
                Map<String, List<SubgroupDataDTO>> responseActionGrpHashMap = new HashMap<>();
                responseActionGrpEvntInfList.forEach(evntInf -> {
                    if (evntInf.getF_RSAT() == 0L) {
                        List<SubgroupDataDTO> dataDTOS = responseActionGrpHashMap.get(Constants.NaN);
                        if (CollectionUtils.isNotEmpty(dataDTOS)) {
                            HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                            hashSet.addAll(responseActionGrpMap.get(evntInf.getF_SGRP().toString()));
                            responseActionGrpHashMap.put(Constants.NaN, new ArrayList<>(hashSet));
                        } else {
                            responseActionGrpHashMap.put(Constants.NaN, responseActionGrpMap.get(evntInf.getF_SGRP().toString()));
                        }
                    } else {
                        RESPONSE_ACTION_DAT responseActionDat = responseActionDatMapper.selectById(evntInf.getF_RSAT());
                        List<SubgroupDataDTO> dataDTOS = responseActionGrpHashMap.get(responseActionDat.getF_RAGP().toString());
                        if (CollectionUtils.isNotEmpty(dataDTOS)) {
                            HashSet<SubgroupDataDTO> hashSet = new HashSet<>(dataDTOS);
                            hashSet.addAll(responseActionGrpMap.get(evntInf.getF_SGRP().toString()));
                            responseActionGrpHashMap.put(responseActionDat.getF_RAGP().toString(), new ArrayList<>(hashSet));
                        } else {
                            responseActionGrpHashMap.put(responseActionDat.getF_RAGP().toString(), responseActionGrpMap.get(evntInf.getF_SGRP().toString()));
                        }
                    }
                });
                map = responseActionGrpHashMap;
                break;
            case INSPECTION_TYPE:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_INSPECTION_TYPE() != null && s.getF_INSPECTION_TYPE() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_INSPECTION_TYPE().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case PTRV_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_REV() != null && s.getF_REV() != 0L)
                        .collect(Collectors.groupingBy(s -> s.getF_REV().toString(),
                                LinkedHashMap::new, Collectors.toList()));
                break;
            default:
        }
        return map;
    }

    /**
     * 根据测试值重新组装子组信息列表
     *
     * @return
     */
    public List<SubgroupDataDTO> reassembly(List<SubgroupDataDTO> subgroupDataDTOList) {
        /*根据测试值重新组装子组*/
        List<SubgroupDataDTO> subgroupDataDTOArrayList = new ArrayList<>();
        subgroupDataDTOList.forEach(subgroupDataDTO -> {
            subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getTestList().forEach(test -> {
                SubgroupDataDTO convert = SubgroupDataConvert.INSTANCE.convert(subgroupDataDTO);
                convert.setTestValDto(test);
                subgroupDataDTOArrayList.add(convert);
            });
        });
        return subgroupDataDTOArrayList;
    }

    /**
     * 获取名称
     * @param analyseTypeDTO
     * @param k
     * @return
     */
    public String getName(AnalyseTypeDTO analyseTypeDTO, String k) {
        String name = I18nUtils.getMessage("NOT_EXIST");
        switch (ParetoAnalyseTypeEnum.getByCode(analyseTypeDTO.getAnalyseType())) {
            case SHIFT_DAT:
                SHIFT_DAT shftDat = shiftDatMapper.selectById(Long.valueOf(k));
                if (shftDat != null) name = shftDat.getF_NAME();
                break;
            case SHIFT_GRP:
                SHIFT_GRP shftGrp = shiftGrpMapper.selectById(Long.valueOf(k));
                if (shftGrp != null) name = shftGrp.getF_NAME();
                break;
            case TEST_DAT:
                TEST_INF testDat = testInfMapper.selectById(Long.valueOf(k));
                if (testDat != null) name = testDat.getF_NAME();
                break;
            case TEST_NO:
                name = I18nUtils.getMessage("TEST_NUMBER")+":" + k;
                break;
            case PART_DAT:
                PART_INF partDat = partInfMapper.selectById(Long.valueOf(k));
                if (partDat != null) name = partDat.getF_NAME();
                break;
            case JOB_DAT:
                JOB_DAT jobDat = jobDatMapper.selectById(Long.valueOf(k));
                if (jobDat != null) name = jobDat.getF_NAME();
                break;
            case JOB_GRP:
                JOB_GRP jobGrp = jobGrpMapper.selectById(Long.valueOf(k));
                if (jobGrp != null) name = jobGrp.getF_NAME();
                break;
            case PRCS_DAT:
                PRCS_INF prcsDat = prcsInfMapper.selectById(Long.valueOf(k));
                if (prcsDat != null) name = prcsDat.getF_NAME();
                break;
            case DESC_DAT:
                DESC_DAT descDat = descDatMapper.selectById(Long.valueOf(k));
                if (descDat != null) name = descDat.getF_NAME();
                break;
            case LOT_DAT:
                LOT_INF partLot = lotInfMapper.selectById(Long.valueOf(k));
                if (partLot != null) name = partLot.getF_NAME();
                break;
            case TIME:
                switch (TimeEnum.getType(analyseTypeDTO.getTimeType())) {
                    case SECOND:
                        Date secondDate = new Date(Long.parseLong(k) * 1000L);
                        name = DateUtils.dateTimeTwo(secondDate);
                        break;
                    case MINUTE:
                        Date minuteDate = new Date(Long.parseLong(k) * 1000L);
                        name = DateUtils.dateTimeFour(minuteDate);
                        break;
                    case HOUR:
                        Date hourDate = new Date(Long.parseLong(k) * 1000L);
                        name = DateUtils.dateTimeFive(hourDate);
                        break;
                    case DAY:
                    case WEEK:
                        Date dayDate = new Date(Long.parseLong(k) * 1000L);
                        name = DateUtils.dateTime(dayDate);
                        break;
                    case MONTH:
                    case QUARTER:
                        Date monthDate = new Date(Long.parseLong(k) * 1000L);
                        name = DateUtils.dateTimeThree(monthDate);
                        break;
                }
                break;
            case STAFF:
                EMPL_INF emplInf = emplInfMapper.selectById(Long.valueOf(k));
                if (emplInf != null) name = emplInf.getF_NAME();
                break;
            case DEF_GRP:
                if (k.equals(Constants.NaN)) {
                    name = k;
                } else {
                    DEF_GRP defGrp = defGrpMapper.selectById(Long.valueOf(k));
                    if (defGrp != null) name = defGrp.getF_NAME();
                }
                break;
            case DEF_DAT:
                if (k.equals(Constants.NaN)) {
                    name = k;
                } else {
                    DEF_DAT defDat = defDatMapper.selectById(Long.valueOf(k));
                    if (defDat != null) name = defDat.getF_NAME();
                }
                break;
            case EVENT_TYPE:
                if (k.equals("1")) {
                    name = I18nUtils.getMessage("TOLERANCE_LIMIT");
                } else {
                    name = I18nUtils.getMessage("CONTROL_LIMIT");
                }
                break;
            case EVENT_NAME:
                name = k;
                break;
            case CAUSE_DAT:
                if (k.equals(Constants.NaN)) {
                    name = k;
                } else {
                    ROOT_CAUSE_DAT rootCauseDat = rootCauseDatMapper.selectById(Long.valueOf(k));
                    if (rootCauseDat != null) name = rootCauseDat.getF_NAME();
                }
                break;
            case CAUSE_GRP:
                if (k.equals(Constants.NaN)) {
                    name = k;
                } else {
                    ROOT_CAUSE_GRP rootCauseGrp = rootCauseGrpMapper.selectById(Long.valueOf(k));
                    if (rootCauseGrp != null) name = rootCauseGrp.getF_NAME();
                }
                break;
            case RESPONSE_ACTION_DAT:
                if (k.equals(Constants.NaN)) {
                    name = k;
                } else {
                    RESPONSE_ACTION_DAT responseActionDat = responseActionDatMapper.selectById(Long.valueOf(k));
                    if (responseActionDat != null) name = responseActionDat.getF_NAME();
                }
                break;
            case RESPONSE_ACTION_GRP:
                if (k.equals(Constants.NaN)) {
                    name = k;
                } else {
                    RESPONSE_ACTION_GRP responseActionGrp = responseActionGrpMapper.selectById(Long.valueOf(k));
                    if (responseActionGrp != null) name = responseActionGrp.getF_NAME();
                }
                break;
            case INSPECTION_TYPE:
                INSPECTION_TYPE_DAT inspectionTypeDat = inspectionTypeDatService.getById(Long.valueOf(k));
                if (inspectionTypeDat != null) name = inspectionTypeDat.getF_NAME();
                break;
            case PTRV_DAT:
                PART_REV partRev = partRevService.getById(Long.valueOf(k));
                if (partRev != null) name = partRev.getF_NAME();
                break;
        }
        return name;
    }

    /**
     * 获取因子
     * @param type
     * @param k
     * @return
     */
    public Double getFactor(Integer type, Long k) {
        Double factor = 1d;
        switch (ParetoAnalyseTypeEnum.getByCode(type)) {
            case SHIFT_DAT:
                SHIFT_DAT shftDat = shiftDatMapper.selectById(k);
                if (shftDat != null) factor = shftDat.getF_FACTOR();
                break;
            case SHIFT_GRP:
                SHIFT_GRP shftGrp = shiftGrpMapper.selectById(k);
                if (shftGrp != null) factor = shftGrp.getF_FACTOR();
                break;
            case TEST_DAT:
                TEST_INF testDat = testInfMapper.selectById(k);
                if (testDat != null) factor = testDat.getF_FACTOR();
                break;
            case PART_DAT:
                PART_INF partDat = partInfMapper.selectById(k);
                if (partDat != null) factor = partDat.getF_FACTOR();
                break;
            case JOB_DAT:
                JOB_DAT jobDat = jobDatMapper.selectById(k);
                if (jobDat != null) factor = jobDat.getF_FACTOR();
                break;
            case JOB_GRP:
                JOB_GRP jobGrp = jobGrpMapper.selectById(k);
                if (jobGrp != null) factor = jobGrp.getF_FACTOR();
                break;
            case PRCS_DAT:
                PRCS_INF prcsDat = prcsInfMapper.selectById(k);
                if (prcsDat != null) factor = prcsDat.getF_FACTOR();
                break;
            case DESC_DAT:
                DESC_DAT descDat = descDatMapper.selectById(k);
                if (descDat != null) factor = descDat.getF_FACTOR();
                break;
            case LOT_DAT:
                LOT_INF partLot = lotInfMapper.selectById(k);
                if (partLot != null) factor = partLot.getF_FACTOR();
                break;
            case DEF_GRP:
                DEF_GRP defGrp = defGrpMapper.selectById(k);
                if (defGrp != null) factor = defGrp.getF_FACTOR();
                break;
            case DEF_DAT:
                DEF_DAT defDat = defDatMapper.selectById(k);
                if (defDat != null) factor = defDat.getF_FACTOR();
                break;
            case CAUSE_DAT:
                ROOT_CAUSE_DAT rootCauseDat = rootCauseDatMapper.selectById(k);
                if (rootCauseDat != null) factor = rootCauseDat.getF_FACTOR();
                break;
            case CAUSE_GRP:
                ROOT_CAUSE_GRP rootCauseGrp = rootCauseGrpMapper.selectById(k);
                if (rootCauseGrp != null) factor = rootCauseGrp.getF_FACTOR();
                break;
            case RESPONSE_ACTION_DAT:
                RESPONSE_ACTION_DAT responseActionDat = responseActionDatMapper.selectById(k);
                if (responseActionDat != null) factor = responseActionDat.getF_FACTOR();
                break;
            case RESPONSE_ACTION_GRP:
                RESPONSE_ACTION_GRP responseActionGrp = responseActionGrpMapper.selectById(k);
                if (responseActionGrp != null) factor = responseActionGrp.getF_FACTOR();
                break;
            case INSPECTION_TYPE:
                INSPECTION_TYPE_DAT inspectionTypeDat = inspectionTypeDatService.getById(k);
                if (inspectionTypeDat != null) factor = inspectionTypeDat.getF_FACTOR();
                break;
        }
        return factor;
    }


    public List<EVNT_INF> getEvntInfList(Set<String> sgrpList) {
        if (sgrpList.size() < 200) {
            /*过程事件*/
            LambdaQueryWrapper<EVNT_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(EVNT_INF::getF_SGRP, sgrpList);
            return evntInfMapper.selectList(queryWrapper);
        }
        int num = sgrpList.size() / 200;
        List<List<String>> subLists = ListUtils.partition(new ArrayList<>(sgrpList), num);
        List<EVNT_INF> arrayList = new ArrayList<>();
        subLists.forEach(s -> {
            LambdaQueryWrapper<EVNT_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(EVNT_INF::getF_SGRP, s);
            List<EVNT_INF> evntInfList = evntInfMapper.selectList(queryWrapper);
            arrayList.addAll(evntInfList);
        });
        return arrayList;
    }
}
