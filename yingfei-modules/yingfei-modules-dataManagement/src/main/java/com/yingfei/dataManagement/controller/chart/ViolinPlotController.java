package com.yingfei.dataManagement.controller.chart;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.DESC_GRPMapper;
import com.yingfei.dataManagement.service.chart.BoxPlotsService;
import com.yingfei.entity.domain.DESC_GRP;
import com.yingfei.entity.domain.DICT_INF;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.BoxPlotsConfigDTO;
import com.yingfei.entity.dto.chart.BoxPlotsDataDTO;
import com.yingfei.entity.dto.chart.BoxPlotsParticularsDTO;
import com.yingfei.entity.enums.BoxPlotsAnalyseTypeEnum;
import com.yingfei.entity.enums.ChartTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 逻辑和小提琴图一样
 */
@Slf4j
@RestController
@Api(tags = "图表:小提琴图API")
@RequestMapping("/violinPlot")
public class ViolinPlotController {

    @Resource
    private BoxPlotsService boxPlotsService;
    @Resource
    private DESC_GRPMapper descGrpMapper;
    @Resource
    private RedisService redisService;

    /**
     * 获取小提琴图详情
     *
     * @return
     */
    @PostMapping("/info")
    @ApiOperation("获取小提琴图详情")
    public R<?> getInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<BoxPlotsDataDTO> info = boxPlotsService.getInfo(subgroupDataSelectionDTO);
        return R.ok(info);
    }


    /**
     * 获取小提琴图分析角度类型
     */
    @PostMapping("/getAnalysisType")
    @ApiOperation("获取小提琴图分析角度类型")
    public R<?> getAnalysisType() {
        Map<String, List<DICT_INF>> cacheMap = redisService.getCacheMap(RedisConstant.DICT_CACHE);
        Map<String, String> map = BoxPlotsAnalyseTypeEnum.getMap(cacheMap);
        return R.ok(map);
    }

    /**
     * 获取描述符组列表
     */
    @PostMapping("/getDescGrpList")
    @ApiOperation("获取描述符组列表")
    public R<?> getDescGrpList(@RequestBody DESC_GRP descGrp) {
        LambdaQueryWrapper<DESC_GRP> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(descGrp.getF_NAME())) {
            queryWrapper.like(DESC_GRP::getF_NAME, descGrp.getF_NAME());
        }
        return R.ok(descGrpMapper.selectList(queryWrapper));
    }

    /**
     * 小提琴图配置更新
     */
    @ApiOperation("小提琴图配置更新")
    @PostMapping("/editConfig/{menuId}/{chartId}")
    public R<?> editConfig(@PathVariable("menuId") Long menuId, String chartId, @RequestBody BoxPlotsConfigDTO boxPlotsConfigDTO) {
        if (boxPlotsConfigDTO == null) throw new BaseException(I18nUtils.getMessage("PLEASE_ENTER_ANALYSIS_PERSPECTIVE"));
        boxPlotsService.editConfig(menuId, chartId, boxPlotsConfigDTO, ChartTypeEnum.VIOLIN_PLOT);
        return R.ok();
    }

    /**
     * 导出小提琴图数据
     */
    @PostMapping("/exportBoxPlotsData/{parameterId}")
    @ApiOperation("导出小提琴图数据")
    public void exportBoxPlotsData(@PathVariable("parameterId") String parameterId, @RequestPart("file") MultipartFile file, HttpServletResponse response) {
        String key = String.format(RedisConstant.BOX_PLOTS_DATA, parameterId);
        /*获取聚合分析子组数据*/
        List<BoxPlotsParticularsDTO> boxPlotsParticularsDTOList = redisService.getCacheList(key);
        if (CollectionUtils.isEmpty(boxPlotsParticularsDTOList)) {
            return;
        }
        ExcelUtil<BoxPlotsParticularsDTO> excelUtil = new ExcelUtil<>(BoxPlotsParticularsDTO.class);
        excelUtil.exportExcel(response, boxPlotsParticularsDTOList, I18nUtils.getMessage("VIOLIN_PLOTS_EXPORT"), Collections.singletonList(file));
    }
}
