package com.yingfei.common.security.config;

import com.alibaba.fastjson2.JSON;
import com.yingfei.common.core.utils.AESUtil;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.security.annotation.SecurityParameter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @desc 请求数据解密
 */
@ControllerAdvice(basePackages = "com.yingfei.auth.controller")
public class DecodeRequestBodyAdvice implements RequestBodyAdvice {

    private static final String aesWebKey = "absoietlj32fai12";

    private static final Logger logger = LoggerFactory.getLogger(DecodeRequestBodyAdvice.class);

    @Override
    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return true;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return body;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) throws IOException {
        try {
            //todo 如果后面要处理时间  这里将前端传的时间转为utc时间
            boolean encode = false;
            if (methodParameter.getMethod().isAnnotationPresent(SecurityParameter.class)) {
                //获取注解配置的包含和去除字段
                SecurityParameter serializedField = methodParameter.getMethodAnnotation(SecurityParameter.class);
                //入参是否需要解密
                encode = serializedField.inDecode();
            }
            if (encode) {
                logger.info("对方法method :【" + methodParameter.getMethod().getName() + "】返回数据进行解密");
                return new MyHttpInputMessage(inputMessage);
            } else {
                return inputMessage;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("对方法method :【" + methodParameter.getMethod().getName() + "】返回数据进行解密出现异常：" + e.getMessage());
            return inputMessage;
        }
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return body;
    }

    class MyHttpInputMessage implements HttpInputMessage {
        private HttpHeaders headers;

        private InputStream body;

        public MyHttpInputMessage(HttpInputMessage inputMessage) throws Exception {
            this.headers = inputMessage.getHeaders();
            this.body = IOUtils.toInputStream(easpString(IOUtils.toString(inputMessage.getBody(), StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
//            this.body = null;
        }

        @Override
        public InputStream getBody() throws IOException {
            return body;
        }

        @Override
        public HttpHeaders getHeaders() {
            return headers;
        }

        /**
         * @param requestData
         * @return
         */
        public String easpString(String requestData) {
            if (requestData != null && !requestData.equals("")) {
                Map<String, String> map = JSON.parseObject(requestData, Map.class);
//                Map<String,String> map = new Gson().fromJson(requestData,new TypeToken<Map<String,String>>() {
//                }.getType());
                // 密文
                String data = map.get("requestData");
                // 加密的aes秘钥
                String encrypted = map.get("encrypted");
                if (StringUtils.isEmpty(data)) {
                    throw new RuntimeException(I18nUtils.getMessage("PARAMETER_REQUEST_DATA_MISSING_EXCEPTION"));
                } else {
                    String content = null;
                    try {
                        content = AESUtil.decryptStr(data, aesWebKey);
                    } catch (Exception e) {
                        throw new RuntimeException(I18nUtils.getMessage("PARAMETER_CONTENT_PARSING_EXCEPTION") + e);
                    }
                    if (StringUtils.isEmpty(content)) {
                        throw new RuntimeException(I18nUtils.getMessage("PARAMETER_REQUEST_DATA_NULL_EXCEPTION"));
                    }
                    return content;
                }
            }
            return "";
        }
    }
}
