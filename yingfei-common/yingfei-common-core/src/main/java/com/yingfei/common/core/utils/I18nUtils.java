package com.yingfei.common.core.utils;

import com.yingfei.common.core.enums.LanguageEnum;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;
import java.util.Objects;

/**
 * 国际化工具类，用于根据KEY获取对应的本地化文本
 */
public class I18nUtils {

    private static MessageSource messageSource;

    /**
     * 初始化工具类，需要注入Spring的MessageSource
     * 通常在Spring配置类中调用此方法
     *
     * @param messageSource Spring的消息源对象
     */
    public static void init(MessageSource messageSource) {
        I18nUtils.messageSource = messageSource;
    }

    /**
     * 根据KEY获取当前环境下的本地化文本
     * 使用默认的Locale（从LocaleContextHolder获取）
     *
     * @param key 消息KEY
     * @return 本地化文本，如果KEY不存在则返回"!key!"形式的字符串
     */
    public static String getMessage(String key) {
        final LanguageEnum language = JudgeUtils.getLanguage();
        Locale locale;
        if (Objects.nonNull(language)) {
            locale = new Locale(language.getMotherLanguage());
        } else {
            locale = LocaleContextHolder.getLocale();
        }
        return getMessage(key, null, null, locale);
    }

    public static String getMessage(String key, LanguageEnum languageEnum) {
        Locale locale;
        if (Objects.nonNull(languageEnum)) {
            locale = new Locale(languageEnum.getMotherLanguage());
        } else {
            locale = LocaleContextHolder.getLocale();
        }
        return getMessage(key, null, null, locale);
    }

    /**
     * 根据KEY和参数获取当前环境下的本地化文本
     *
     * @param key 消息KEY
     * @param args 文本中的参数（用于替换{0}、{1}等占位符）
     * @return 本地化文本
     */
    public static String getMessage(String key, Object[] args) {
        return getMessage(key, args, null, LocaleContextHolder.getLocale());
    }

    /**
     * 根据KEY和指定Locale获取本地化文本
     *
     * @param key 消息KEY
     * @param locale 指定的语言环境
     * @return 本地化文本
     */
    public static String getMessage(String key, Locale locale) {
        return getMessage(key, null, null, locale);
    }

    /**
     * 完整方法：根据KEY、参数、默认值和Locale获取本地化文本
     *
     * @param key 消息KEY
     * @param args 文本中的参数
     * @param defaultMessage 当KEY不存在时的默认文本
     * @param locale 语言环境
     * @return 本地化文本
     */
    public static String getMessage(String key, Object[] args, String defaultMessage, Locale locale) {
        if (messageSource == null) {
            // 如果MessageSource未初始化，返回默认消息而不是抛出异常
            System.err.println("警告: I18nUtils 未初始化，返回默认消息: " + defaultMessage);
            return defaultMessage != null ? defaultMessage : null;
        }
        try {
            final String message = messageSource.getMessage(key, args, defaultMessage, locale);
            return message;
        } catch (Exception e) {
            System.err.println("获取国际化消息失败，key: " + key + ", 错误: " + e.getMessage());
            return defaultMessage != null ? defaultMessage : null;
        }
    }
}
