package com.yingfei.common.core.domain;

import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.utils.I18nUtils;
import io.swagger.annotations.ApiModelProperty;

/**
 * 响应信息主体
 */
public class R<T> {
    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int SUCCESS = Constants.SUCCESS;

    /**
     * 失败
     */
    public static final int FAIL = Constants.FAIL;

    @ApiModelProperty("状态码")
    private int code;

    @ApiModelProperty("消息")
    private String message;

    @ApiModelProperty("数据对象")
    private T data;

    public static <T> R<T> ok() {
        return restResult(null, SUCCESS, I18nUtils.getMessage("SUCCESS"));
    }

    public static <T> R<T> ok(T data) {
        return restResult(data, SUCCESS, I18nUtils.getMessage("SUCCESS"));
    }

    public static <T> R<T> ok(T data, String msg) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> R<T> fail() {
        return restResult(null, FAIL, I18nUtils.getMessage("FAIL"));
    }

    public static <T> R<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> R<T> fail(T data) {
            return restResult(data, FAIL, I18nUtils.getMessage("FAIL"));
    }

    public static <T> R<T> fail(T data, String msg) {
        return restResult(data, FAIL, msg);
    }

    public static <T> R<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    private static <T> R<T> restResult(T data, int code, String msg) {
        R<T> apiResult = new R<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMessage(msg);
        return apiResult;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String msg) {
        this.message = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> Boolean isError(R<T> ret) {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(R<T> ret) {
        return R.SUCCESS == ret.getCode();
    }
}
