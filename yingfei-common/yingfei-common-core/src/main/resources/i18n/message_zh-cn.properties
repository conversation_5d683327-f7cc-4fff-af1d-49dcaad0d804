#validate Messages:

SUCCESS = 操作成功
FAIL = 操作失败
DELETE = 删除


sys_exception=系统异常
service_not_found=服务未找到
the_user_has_no_permission=该功能用户没有权限

# BpmProcessInstanceDeleteReasonEnum
REJECT_TASK = 不通过任务，原因：{}
CANCEL_TASK = 主动取消任务，原因：{}
MULTI_TASK_END = 系统自动取消，原因：多任务审批已经满足条件，无需审批该任务
REJECT_TASK_REASON_PREFIX = 不通过任务，原因：

INVALID_TYPE =无效type

# AuthExceptionEnum
AUTH_PERMISS_EXCEPTION = "权限不足
AUTH_MARK_DUPLICATION_EXCEPTION = "权限标识重复
AUTH_PASSWORD_EXCEPTION = "登录密码错误
AUTH_ACCOUNT_EXPIRED_EXCEPTION = "该账户已过期
AUTH_USER_NOT_LOGIN_EXCEPTION = "用户未登录
IP_BLACKLIST =  "该IP已被列入系统黑名单
LOGIN_ACCOUNT_DOES_NOT_EXIST = "登录账号不存在
AUTH_ACCOUNT_PASSWORD_EMPTY_EXCEPTION = "手机号/密码必须填写
LOGIN_ACCOUNT_UP_TO_THE_LIMIT = 该用户登录失败次数已达上限,请1小时后重试!
LOGIN_USER_COUNT_REACH_LIMIT = 该系统登录人数已达上限

# CommonExceptionEnum
PARAMETER_MISSING_EXCEPTION = 请求参数缺失
PRESENCE_SUBDATA__EXCEPTION = 该组存在子数据
DATA_NOT_FOUND_EXCEPTION = 该数据不存在
IMPORT_DATA_NOT_NULL_EXCEPTION = 导入数据不能为空
MQ_SEND_EXCEPTION = 消息发送失败
DO_NOT_SUBMIT_DATA_TWICE = 请勿重复提交数据
ACCESS_TOKEN_FAILED = 获取access_token失败
SEND_CONFIG_NOT_EXIST = 消息发送配置未配置
THE_IMPORTED_FILE_TYPE_IS_NOT_SUPPORTED = 导入文件类型不支持
FEIGN_ERROR = feign调用失败
DATA_ALREADY_EXISTS_EXCEPTION = 数据已存在
UNSUPPORTED_DATABASE_TYPE_EXCEPTION = 不支持的数据库类型

# DataCollectionExceptionEnum
NUMBER_OF_COLUMNS_DOES_NOT_MATCH_EXCEPTION = 导入文件列数不匹配,请检查文件格式
START_LINE_EXCEPTION = 请检查起始行是否正确
TASK_TIME_NOT_REACHED = 任务未到开始时间，不能恢复

# DataManagementExceptionEnum
PRODUCT_NAME_DUPLICATION_EXCEPTION = 产品名称重复
PROCESS_NAME_DUPLICATION_EXCEPTION = 过程名称重复
TEST_NAME_DUPLICATION_EXCEPTION = 测试名称重复
TAG_GRP_NAME_DUPLICATION_EXCEPTION = 标签组名称重复
TAG_NAME_DUPLICATION_EXCEPTION = 标签名称重复
SHIFT_GRP_NAME_DUPLICATION_EXCEPTION = 班次组名称重复
SHIFT_NAME_DUPLICATION_EXCEPTION = 班次名称重复
PRODUCT_BATCH_NAME_DUPLICATION_EXCEPTION = 产品批次名称重复
PRODUCT_SERIAL_NUMBER_NAME_DUPLICATION_EXCEPTION = 产品序列号名称重复
JOB_GRP_NAME_DUPLICATION_EXCEPTION = 工单组名称重复
JOB_NAME_DUPLICATION_EXCEPTION = 工单名称重复
DESC_GRP_NAME_DUPLICATION_EXCEPTION = 自定义描述符组名称重复
DESC_NAME_DUPLICATION_EXCEPTION = 自定义描述符名称重复
DEF_GRP_NAME_DUPLICATION_EXCEPTION = 缺陷代码组名称重复
DEF_NAME_DUPLICATION_EXCEPTION = 缺陷代码名称重复
ROOT_CAUSE_GRP_NAME_DUPLICATION_EXCEPTION = 异常原因组名称重复
ROOT_CAUSE_NAME_DUPLICATION_EXCEPTION = 异常原因名称重复
RESPONSE_ACTION_GRP_NAME_DUPLICATION_EXCEPTION = 改善措施组名称重复
RESPONSE_ACTION_NAME_DUPLICATION_EXCEPTION = 改善措施名称重复
PARAMETER_SET_NAME_DUPLICATION_EXCEPTION = 参数集名称重复
PROCESSING_TEMPLATE_NAME_DUPLICATION_EXCEPTION = 数据处理模板名称重复
ACTIVED_RULE_TEMPLATE_NAME_DUPLICATION_EXCEPTION = 报警规则模板名称重复
WARNING_LIMIT_EXCEPTION = 报警限配置错误
REASONABLE_LIMIT_EXCEPTION = 合理限配置错误
USL_LSL_EXCEPTION = 公差限配置错误
PRODUCT_REV_NAME_DUPLICATION_EXCEPTION = 产品版本名称重复
MANUFACTURING_PROCESS_NAME_DUPLICATION_EXCEPTION = 流程图结构名称重复
DELETE_THE_SUBPLAN_FIRST_EXCEPTION = 请先删除子计划
CLEAR_THE_CACHE_SUBGROUP_EXCEPTION = 清除缓存子组失败
PROCESS_DEFINITION_KEY_NOT_MATCH = 流程定义的标识期望是({})，当前是({})，请修改 BPMN 流程图
PROCESS_DEFINITION_NAME_NOT_MATCH = 流程定义的名称期望是({})，当前是({})，请修改 BPMN 流程图
MODEL_KEY_VALID = 流程标识格式不正确，需要以字母或下划线开头，后接任意字母、数字、中划线、下划线、句点！
MODEL_KEY_EXISTS = 已经存在流程标识为【{}】的流程
MODEL_NOT_EXISTS = 流程模型不存在
MODEL_DEPLOY_FAIL_FORM_NOT_CONFIG = 部署流程失败，原因：流程表单未配置，请点击【修改流程】按钮进行配置
FORM_NOT_EXISTS = 动态表单不存在
PROCESS_DEFINITION_NOT_EXISTS = 流程定义不存在
PROCESS_INSTANCE_NOT_EXISTS = 流程实例不存在
TASK_COMPLETE_FAIL_NOT_EXISTS = 审批任务失败，原因：该任务不处于未审批
TASK_COMPLETE_FAIL_ASSIGN_NOT_SELF = 审批任务失败，原因：该任务的审批人不是你
PROCESS_DEFINITION_IS_SUSPENDED = 流程定义处于挂起状态
PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS = 流程取消失败，流程不处于运行中
PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF = 流程取消失败，该流程不是你发起的
TASK_ASSIGN_RULE_EXISTS = 流程({}) 的任务({}) 已经存在分配规则
TASK_ASSIGN_RULE_NOT_EXISTS = 流程任务不存在
TASK_UPDATE_FAIL_NOT_MODEL = 只有流程模型的任务分配规则，才允许被修改
MODEL_DEPLOY_FAIL_TASK_ASSIGN_RULE_NOT_CONFIG = 部署流程失败，原因：用户任务({})未配置分配规则，请点击【修改流程】按钮进行配置
TASK_ASSIGN_FORM_EXISTS = 流程({}) 的任务({}) 已经存在分配表单
TASK_CREATE_FAIL_NO_CANDIDATE_USER = 操作失败，原因：找不到任务的审批人！
GAUGE_AGENT_NAME_DUPLICATION_EXCEPTION = 量具Agent名称或硬件id已存在
GAUGE_HARDWARE_ID_NOT_EXISTS = 量具硬件id不存在
GAUGE_FORMAT_NAME_DUPLICATION_EXCEPTION = 量具解析规则名称重复
GAUGE_INTERFACE_NAME_DUPLICATION_EXCEPTION = 量具接口配置名称重复
GAUGE_CONNECTION_NOT_EXISTS = 量具连接配置不存在
GAUGE_DEVICE_NOT_EXISTS = 量具设备不存在
GAUGE_FORMAT_NOT_EXISTS = 量具解析配置不存在
PLEASE_CHECK_THE_TERMINAL_CONFIGURATION = 请检查终结符配置
TERMINAL_SYMBOL_NOT_EXISTS = 不存在终结符
PLEASE_CHECK_THE_INITIATOR_CONFIGURATION = 请检查启动符配置
THE_CHANNEL_NUMBER_DOES_NOT_MATCH = 频道号不匹配
CTRL_DUPLICATION_EXCEPTION = 控制限重复
SPEC_DUPLICATION_EXCEPTION = 公差限重复
PARAMETER_SET_NOT_EXISTS = 参数集不存在
MENU_ALREADY_ASSOCIATED_ANALYSIS_TEMPLATE = 菜单已关联分析模板
CHART_TYPE_NOT_EXISTS = 图表类型不存在
QUADRANT_CHART_CONFIG_NOT_EXISTS = 象限图配置不存在
ANALYSIS_DASHBOARD_NOT_EXISTS = 分析页面不存在
CHART_CONFIG_NOT_EXISTS = 图表配置不存在
DICT_CODE_DUPLICATION_EXCEPTION = 字典标识和内容重复
JOB_GRP_NOT_EXISTS = 工作组不存在
SHIFT_GRP_NOT_EXISTS = 班次组不存在
DESC_GRP_NOT_EXISTS = 描述符组不存在
DES_GRP_NOT_EXISTS = 缺陷代码组不存在
MENU_NOT_ASSOCIATED_PARAMETER_SET = 菜单未关联参数集
DEF_GRP_NOT_ALLOW_CREATE_DEF = 该缺陷代码组不允许创建缺陷代码
DESC_GRP_NOT_ALLOW_CREATE_DESC = 该描述符组不允许创建描述符
JOB_GRP_NOT_ALLOW_CREATE_JOB = 该工作组不允许创建工作
DB_CONFIG_NOT_EXISTS = 数据库配置不存在
TEST_SQL_COLUMN_NAME_DUPLICATION_EXCEPTION = 测试SQL有列名相同
ANALYSIS_DASHBOARD_TEMPLATE_NAME_DUPLICATION_EXCEPTION = 模板名称重复
TEST_VAL_IS_NULL = 测试值为空
PRODUCT_TEST_DUPLICATION_EXCEPTION = 产品测试重复
THE_CACHE_IS_BEING_PROCESSED = 该缓存正在处理,请稍后再试
MFPS_NOT_EXISTS = 工艺流程不存在
MFND_NOT_EXISTS = 工艺节点不存在
PLAN_NOT_EXISTS = 检查计划不存在
CHILD_NOT_EXISTS = 子计划不存在
MFPS_PLNT_INCONFORMITY = 工艺流程工厂与所选工厂不一致
PLEASE_SELECT_FACTORY = 请选择工厂
STRUCTURE_UNDER_CONFIGURATION = 结构配置不全
INSPECTION_TYPE_GRP_NAME_DUPLICATION_EXCEPTION = 检验类型组名称重复
INSPECTION_TYPE_DAT_NAME_DUPLICATION_EXCEPTION = 检验类型名称重复
START_TIME_GT_END_TIME = 开始日期大于结束日期
PARAMETER_SET_RELEVANCE_ANALYSIS_DASHBOARD = 参数集已关联分析页面!
INSPECTION_TYPE_DAT_DUPLICATION_EXCEPTION = 有检验类型在使用
MONITOR_SAVE_NUM_EXCEPTION = 监控缓存子组数配置有误
MODEL_UNPUBLISHED = 流程模型未发布
EFFECTIVE_TOLERANCE_LIMIT = 界限未配置
UNIT_NAME_DUPLICATION_EXCEPTION = 单位名称重复
MAPPING_DATA_NOT_EXIST = 映射数据不存在
UWL_GT_USL_EXCEPTION = 报警限不能大于公差限
URL_LT_USL_EXCEPTION = 合理限不能小于公差限
SWITCH_LANGUAGE_NOT_EXISTS = 切换语言不存在
NOT_CLOSE_PART_ALL_VERSION = 禁止关闭产品下所有版本
CHART_TYPE_MISMATCH_EXCEPTION = 图表类型不匹配

# SystemExceptionEnum
LENGTH_EXCEPTION = 权限不足
INPUT_STREAM_EXCEPTION = 读取文件错误
ACCOUNT_EXIST_EXCEPTION = 登录账号已存在
EMPLOYEE_CODE_EXIST_EXCEPTION = 员工工号已存在
PASSWORD_NOT_NULL_EXCEPTION = 密码不能为空
OLD_PASSWORD_NOT_SAME_EXCEPTION = 原密码不一致
PASSWORD_CANNOT_SAME_HISTORY_EXCEPTION = 新密码不能与历史密码相同
DEPT_NAME_DUPLICATION_EXCEPTION = 部门名称重复
ROLE_BIND_EXCEPTION = 所选角色已有绑定账户
ROLE_NAME_DUPLICATION_EXCEPTION = 角色名称重复
USER_HIER_NOT_EXISTS = 用户层级不存在
ACCOUNT_NOT_EXIST_EXCEPTION = 账号不存在或未激活
PERMISSION_BIND_EXCEPTION = 所选权限模板已有绑定角色
EMAIL_EXIST_EXCEPTION = 员工邮箱已存在
WECHAT_EXIST_EXCEPTION = 员工企业微信已存在
DING_DING_EXIST_EXCEPTION = 员工钉钉账号已存在
MENU_ANALYSIS_PAGE_EXCEPTION = 该菜单有分析页面关联
HIERARCHY_TYPE_EXCEPTION = 层级类型错误





TOKEN_CANNOT_BE_EMPTY = 令牌不能为空
TOKEN_EXPIRED_OR_INVALID = 令牌已过期或验证不正确!
LOGIN_STATUS_EXPIRED = 登录状态已过期
TOKEN_VERIFICATION_FAILED = 令牌验证失败


SYSTEM_LOGIN_USER_COUNT_REACHED_LIMIT =该系统登录人数已达上限!
NO_ACCESS_PERMISSION_PLEASE_CONTACT_ADMIN_FOR_AUTHORIZATION = 没有访问权限，请联系管理员授权

NO_INTERNAL_ACCESS_PERMISSION_ACCESS_NOT_ALLOWED = 没有内部访问权限，不允许访问
NO_USER_INFO_SET_ACCESS_NOT_ALLOWED= 没有设置用户信息，不允许访问   

REFLECTION_IMPLEMENTATION_CLASS_EXCEPTION = 反射实现类异常
ACCESS_TO_THE_METHOD_IS_DENIED = 拒绝访问该方法
EXCEPTION_OCCURRED_WHILE_EXECUTING_THE_ALARM_METHOD = 执行报警方法异常
SAVE_CONDITION_PARSING_ERROR = 保存条件解析错误
FAILED_TO_GET_EXPRESSION_TRIGGER = 获取表达式触发器失败
FAILED_TO_CREATE_SCHEDULED_TASK = 创建定时任务失败
FAILED_TO_UPDATE_SCHEDULED_TASK = 更新定时任务失败
FAILED_TO_EXECUTE_SCHEDULED_TASK_IMMEDIATELY = 立即执行定时任务失败
FAILED_TO_PAUSE_SCHEDULED_TASK = 暂停定时任务失败
FAILED_TO_RESUME_SCHEDULED_TASK = 恢复定时任务失败
FAILED_TO_DELETE_SCHEDULED_TASK = 删除定时任务失败
PLEASE_ENTER_ANALYSIS_PERSPECTIVE = 请输入分析角度
PLEASE_CONVERT_TO_XLSX_FORMAT_FOR_IMPORT = 请转为 xlsx 格式导入
HIERARCHY_PARSING_ERROR = 层级解析错误

VIOLIN_PLOTS_EXPORT = 小提琴图导出
BOX_PLOTS_EXPORT = 箱线图导出
STREAM_TREND_EXPORT = 实时能力趋势图导出
REAL_TIME_CAPABILITY_ANALYSIS = 实时能力分析



# 新增业务逻辑中的中文信息
MD5_NOT_EXISTS = MD5不存在！
USER_PASSWORD_MUST_FILL = 用户/密码必须填写
ACCOUNT_LENGTH_BETWEEN_2_20 = 账户长度必须在2到20个字符之间
PASSWORD_LENGTH_BETWEEN_5_20 = 密码长度必须在5到20个字符之间
IMPORT_FAILED = 导入失败
GET_USER_FAILED = 获取用户失败
GET_HIERARCHY_FAILED = 获取层级失败

# 数据分析相关
WORK = 工作
BATCH = 批次
SHIFT = 班次
VERSION = 版本
SERIAL_NUMBER = 序列号
DEFECT_CODE = 缺陷代码
BILATERAL_TOLERANCE = 双边公差
UNILATERAL_UPPER_TOLERANCE = 单边上公差
UNILATERAL_LOWER_TOLERANCE = 单边下公差
PRODUCT = 产品
PROCESS = 过程
TEST = 测试
TIME_RANGE = 时间范围
DATA_STATISTICS_METHOD = 数据统计方式
BASED_ON_TIME_GROUPING = 基于时间分组
TIME_INTERVAL = 时间间隔
BASED_ON_SUBGROUP_QUANTITY = 基于子组数量
GROUP_COUNT = 分组数
OFFSET = 偏移量
REAL_TIME_CAPABILITY_ANALYSIS_EXPORT = 实时能力分析

SUBGROUP_ID=子组ID
SUBGROUP_TIME=子组时间
WORK_ORDER=工单
TEST_SEQUENCE_NUMBER=测试序号
ACTUAL_VALUE=实测值
CPK_TARGET_VALUE_1=CPK目标值1
CPK_TARGET_VALUE_2=CPK目标值2
CPK=cpk
CP=cp








# 系统常量信息
LOGIN_SUCCESS_MESSAGE = 登录成功
LOGOUT_MESSAGE = 退出成功
DATA_VOLUME_TOO_LARGE = 数据量过大
AUTHENTICATION_CENTER_STARTUP_SUCCESS = 认证授权中心启动成功

# 异常常量信息
PARAM_DEFICIENCY = 参数缺失
SPEC_LIM_NOT_EXIST = 公差限信息不存在
SUBUNIT_NOT_EXIST = 暂无该子组信息
CUSTOM_PARAM_NOT_EXIST = 自定义参数不存在
USER_NOT_EXIST = 用户不存在!

# 文件操作异常
FILE_UPLOAD_FAILED = 文件上传失败
FILE_SHEET_NOT_EXIST = 文件sheet不存在
APPLICATION_CONTEXT_NULL = "applicaitonContext属性为null,请检查是否注入了SpringContextHolder!"

# 通用信息
SECOND = 秒
MINUTE = 分钟
HOUR = 小时
DAY = 天
WEEK = 周
MONTH = 月
YEAR = 年
QUARTER = 季度

ANALYSIS_REPORT = 分析报告
PARAMETER_SET = 参数集
DATE_RANGE = 日期范围
EXPORT_DATE = 导出日期
EXPORT_USER = 导出用户
DATA_DETAIL = 数据明细

TOLERANCE_LIMIT = 公差限
CONTROL_LIMIT = 控制限
NOT_EXIST = 不存在
TEST_NUMBER = 测试编号

PROCESS_TASK_NO_MATCHING_RULE_FOUND = 流程任务 ({}/{}/{}) 找不到符合的任务规则
PROCESS_TASK_TOO_MANY_RULES_FOUND = 流程任务 ({}/{}/{}) 找到过多任务规则 ({})

PARAMETER_REQUEST_DATA_MISSING_EXCEPTION = 参数【requestData】缺失异常！
PARAMETER_REQUEST_DATA_NULL_EXCEPTION = 参数【requestData】解析参数空指针异常!
PARAMETER_CONTENT_PARSING_EXCEPTION = 参数【content】解析异常！
MONTHLY_DATA_ABNORMAL = 月份数据异常:
AUTHORIZATION_VERIFICATION_FAILED_MACHINE_NUMBER_NOT_EXIST = 授权校验失败，机器编号不存在
AUTHORIZATION_VERIFICATION_FAILED_SYSTEM_SERVICE_STOPPED = 授权校验失败，系统服务已停止！该机器编号为:-------->
MANUALLY_TERMINATE_FLOW = 手动终止流
TIMEOUT = 超时

# 数字转换相关
JIAO = 角
FEN = 分
LING = 零
YI = 壹
ER = 贰
SAN = 叁
SI = 肆
WU = 伍
LIU = 陆
QI = 柒
BA = 捌
JIU = 玖
SHI = 拾
BAI = 佰
QIAN = 仟
WAN = 万
YI_UNIT = 亿
YUAN = 元

# 加密相关
AES_ENCRYPTION = AES
DES_ENCRYPTION = DES

# 通用描述
FRONT_THREE_DIGITS = 前3位
MIDDLE_THREE_DIGITS = 中间3位
LAST_THREE_DIGITS = 后3位

# 系统配置
CREATE_DEFAULT_MESSAGE_SOURCE_BEAN = 创建默认 MessageSource Bean
I18N_UTILS_INITIALIZED = I18nUtils 通过 I18nAutoConfiguration 初始化完成







