package com.yingfei.common.core.utils.poi;

import com.yingfei.common.core.annotation.Excel;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ExcelUtil 测试类
 * 验证Excel导出功能，特别是数据完整性问题
 */
public class ExcelUtilTest {

    /**
     * 测试用的DTO类
     */
    public static class TestDataDTO {
        @Excel(name = "ID")
        private Long id;
        
        @Excel(name = "名称")
        private String name;
        
        @Excel(name = "值")
        private Double value;

        public TestDataDTO() {}

        public TestDataDTO(Long id, String name, Double value) {
            this.id = id;
            this.name = name;
            this.value = value;
        }

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Double getValue() { return value; }
        public void setValue(Double value) { this.value = value; }
    }

    @Test
    void testExportWithFileList_ShouldNotLoseLastRow() throws IOException {
        // 创建58条测试数据
        List<TestDataDTO> testData = new ArrayList<>();
        for (int i = 1; i <= 58; i++) {
            testData.add(new TestDataDTO((long) i, "测试数据" + i, (double) i * 10));
        }

        // 创建模拟文件
        MockMultipartFile mockFile = new MockMultipartFile(
            "test", 
            "test.png", 
            "image/png", 
            "test image content".getBytes()
        );
        List<MultipartFile> fileList = Collections.singletonList(mockFile);

        // 创建ExcelUtil实例
        ExcelUtil<TestDataDTO> excelUtil = new ExcelUtil<>(TestDataDTO.class);
        
        // 使用ByteArrayOutputStream来捕获输出
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        // 初始化ExcelUtil
        excelUtil.init(testData, "测试表", "测试标题", Excel.Type.EXPORT);
        
        // 调用writeSheet方法
        excelUtil.writeSheet(fileList, null);
        
        // 验证工作簿内容
        Workbook workbook = excelUtil.wb;
        Sheet sheet = workbook.getSheetAt(0);
        
        // 计算数据行数（排除标题行和表头行）
        int expectedDataRows = 58;
        int titleRows = 1; // 标题行
        int headerRows = 1; // 表头行
        int expectedTotalRows = titleRows + headerRows + expectedDataRows;
        
        // 验证总行数（不包括图片行）
        int actualDataRows = 0;
        for (int i = titleRows + headerRows; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null && row.getCell(0) != null && 
                row.getCell(0).getCellType() != org.apache.poi.ss.usermodel.CellType.BLANK) {
                // 检查是否是数据行（第一列有ID值）
                try {
                    if (row.getCell(0).getNumericCellValue() > 0) {
                        actualDataRows++;
                    }
                } catch (Exception e) {
                    // 如果不是数字，可能是图片行，跳过
                }
            }
        }
        
        // 验证数据完整性
        assertEquals(expectedDataRows, actualDataRows, 
            "应该有58行数据，但实际只有" + actualDataRows + "行");
        
        // 验证最后一行数据
        Row lastDataRow = null;
        for (int i = sheet.getLastRowNum(); i >= 0; i--) {
            Row row = sheet.getRow(i);
            if (row != null && row.getCell(0) != null) {
                try {
                    double cellValue = row.getCell(0).getNumericCellValue();
                    if (cellValue == 58.0) {
                        lastDataRow = row;
                        break;
                    }
                } catch (Exception e) {
                    // 继续查找
                }
            }
        }
        
        assertNotNull(lastDataRow, "应该能找到ID为58的最后一行数据");
        assertEquals(58.0, lastDataRow.getCell(0).getNumericCellValue(), 
            "最后一行的ID应该是58");
        assertEquals("测试数据58", lastDataRow.getCell(1).getStringCellValue(), 
            "最后一行的名称应该是'测试数据58'");
        assertEquals(580.0, lastDataRow.getCell(2).getNumericCellValue(), 
            "最后一行的值应该是580");
    }

    @Test
    void testExportWithoutFileList_ShouldHaveAllRows() {
        // 创建58条测试数据
        List<TestDataDTO> testData = new ArrayList<>();
        for (int i = 1; i <= 58; i++) {
            testData.add(new TestDataDTO((long) i, "测试数据" + i, (double) i * 10));
        }

        // 创建ExcelUtil实例
        ExcelUtil<TestDataDTO> excelUtil = new ExcelUtil<>(TestDataDTO.class);
        
        // 初始化ExcelUtil
        excelUtil.init(testData, "测试表", "测试标题", Excel.Type.EXPORT);
        
        // 调用writeSheet方法（不传文件列表）
        excelUtil.writeSheet(new ArrayList<>(), null);
        
        // 验证工作簿内容
        Workbook workbook = excelUtil.wb;
        Sheet sheet = workbook.getSheetAt(0);
        
        // 计算数据行数
        int actualDataRows = 0;
        int titleRows = 1;
        int headerRows = 1;
        
        for (int i = titleRows + headerRows; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null && row.getCell(0) != null) {
                try {
                    if (row.getCell(0).getNumericCellValue() > 0) {
                        actualDataRows++;
                    }
                } catch (Exception e) {
                    // 跳过非数字行
                }
            }
        }
        
        // 验证数据完整性
        assertEquals(58, actualDataRows, 
            "没有文件时应该有58行数据，但实际只有" + actualDataRows + "行");
    }
}
