package com.yingfei.auth.service;

import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.yingfei.auth.form.LoginBody;
import com.yingfei.common.core.constant.CacheConstants;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.constant.SecurityConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.LoginTypeEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.ServiceException;
import com.yingfei.common.core.exception.enums.AuthExceptionEnum;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.text.Convert;
import com.yingfei.common.core.utils.AESUtil;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.ip.IpUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.entity.dto.globalConfig.AccountConfig;
import com.yingfei.entity.dto.globalConfig.SysyemGlobalConfig;
import com.yingfei.entity.model.LoginUser;
import com.yingfei.system.api.RemoteGlobalConfigInfService;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;

/**
 * 登录校验方法
 */
@Component
@Slf4j
public class SysLoginService {
    @Resource
    private RemoteUserService remoteUserService;

//    @Resource
//    private SysPasswordService passwordService;

    @Resource
    private SysRecordLogService recordLogService;

    @Resource
    private RedisService redisService;
    @Resource
    private RemoteGlobalConfigInfService remoteGlobalConfigInfService;
    /**
     * 登录
     */
    public LoginUser login(LoginBody form) {
        String account = form.getAccount();
        String password = form.getPassword();

        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(account, password)) {
            throw new BusinessException(AuthExceptionEnum.AUTH_ACCOUNT_PASSWORD_EMPTY_EXCEPTION);
        }

        /*判断用户是否登录锁定*/
        String lockKey = String.format(RedisConstant.ACCOUNT_LOGIN_FAILURES_LOCK, account);
        Object o = redisService.get(lockKey);
        if (o != null) {
            throw new BusinessException(AuthExceptionEnum.LOGIN_ACCOUNT_UP_TO_THE_LIMIT);
        }

        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            throw new BusinessException(AuthExceptionEnum.IP_BLACKLIST);
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getLoginUserInfo(account, SecurityConstants.INNER);

        if (StringUtils.isNull(userResult)) {
            throw new BusinessException(AuthExceptionEnum.LOGIN_ACCOUNT_DOES_NOT_EXIST);
        }
//        EmplSecurityConfigDTO emplSecurityConfigDTO = redisService.getCacheObject(RedisConstant.ACCOUNT_SECURITY_CONFIGURATION);
        SysyemGlobalConfig systemConfig = null;
        try {
            systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
        } catch (Exception e) {
            log.error("获取系统配置-账户安全 信息失败 ex:{}",e.getMessage(), e);
            throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
        }
        final AccountConfig accountConfig = systemConfig.getAccountConfig();

//        if (emplSecurityConfigDTO == null) emplSecurityConfigDTO = new EmplSecurityConfigDTO();


        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMessage());
        }

        LoginUser loginUser = userResult.getData();

        /*验证密码*/
        String key = String.format(RedisConstant.ACCOUNT_LOGIN_FAILURES, account);
        try {
            /*对账户密码解密判断密码是否一致*/
            String s = AESUtil.decryptStr(loginUser.getSysUser().getF_PSW(), AESUtil.defaultAesKey);
            String[] split = s.split(Constants.SEPARATION);
            if (!split[3].equals(password)) {
                if (accountConfig.getLoginFailures() != null) {
                    long increment = redisService.increment(key, 1, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
                    if (accountConfig.getLoginFailures() == increment) {
                        /*清除计数*/
                        redisService.deleteObject(key);
                        /*添加锁定名单*/
                        redisService.set(String.format(RedisConstant.ACCOUNT_LOGIN_FAILURES_LOCK, account), account, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
                    }
                }
                throw new BusinessException(AuthExceptionEnum.AUTH_PASSWORD_EXCEPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(AuthExceptionEnum.AUTH_PASSWORD_EXCEPTION);
        }

        /*判断账户是否过期*/
        Date fEptm = loginUser.getSysUser().getF_EPTM();
        if (DateUtils.getNowDate().getTime() > fEptm.getTime()) {
            throw new BusinessException(AuthExceptionEnum.AUTH_ACCOUNT_EXPIRED_EXCEPTION);
        }
        recordLogService.recordLogininfor(loginUser.getSysUser().getF_CODE(), Constants.LOGIN_SUCCESS, Constants.LOGIN_SUCCESS_MESSAGE);
        /*清除计数*/
        redisService.deleteObject(key);
        return loginUser;
    }

    /* 账户登录信息(F_EMPL 账户在EMPL_INF表的主键，F_CODE 登录名，F_PWD 登录密码[MD5加密后去掉最后一位])*/
    public static String stringToUpperMD5(String data) {
        byte[] mdBytes;
        try {
            mdBytes = MessageDigest.getInstance("MD5").digest(
                    data.getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(I18nUtils.getMessage("MD5_NOT_EXISTS"));
        }
        StringBuilder mdCode = new StringBuilder(new BigInteger(1, mdBytes).toString(16));
        if (mdCode.length() < 32) {
            int a = 32 - mdCode.length();
            for (int i = 0; i < a; i++) {
                mdCode.insert(0, "0");
            }
        }
        String upperCase = mdCode.toString().toUpperCase();
        return upperCase.substring(0, 31);
    }

    private LoginTypeEnum getLoginType() {
        UserAgent userAgent = UserAgentUtil.parse(SecurityUtils.getUserAgent());
        return userAgent.isMobile() ? LoginTypeEnum.MOBILE : LoginTypeEnum.PC;
    }


    public void logout(String loginName) {
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, Constants.LOGOUT_MESSAGE);
    }
//
//    /**
//     * 注册
//     */
//    public void register(String username, String password) {
//        // 用户名或密码为空 错误
//        if (StringUtils.isAnyBlank(username, password)) {
//            throw new ServiceException("用户/密码必须填写");
//        }
//        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
//                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
//            throw new ServiceException("账户长度必须在2到20个字符之间");
//        }
//        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
//                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
//            throw new ServiceException("密码长度必须在5到20个字符之间");
//        }
//
//        // 注册用户信息
//        SysUser sysUser = new SysUser();
//        sysUser.setUserName(username);
//        sysUser.setNickName(username);
//        sysUser.setPassword(SecurityUtils.encryptPassword(password));
//        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);
//
//        if (R.FAIL == registerResult.getCode()) {
//            throw new ServiceException(registerResult.getMessage());
//        }
//        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");
//    }
}
